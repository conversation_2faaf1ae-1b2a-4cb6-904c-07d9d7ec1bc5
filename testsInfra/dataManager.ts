import Consts from './innerInfra/dataManipulation/consts';
import DataConverter from './innerInfra/dataManipulation/dataConverter';
import DataGenerator from './innerInfra/dataManipulation/dataGenerator';

export default class DataManager {
  private static _dataGenerator: DataGenerator | null = null;
  private static _dataConverter: DataConverter | null = null;
  private static _consts: Consts | null = null;

  static get DataGenerator(): DataGenerator {
    return this._dataGenerator ?? (this._dataGenerator = new DataGenerator());
  }

  static get DataConverter(): DataConverter {
    return this._dataConverter ?? (this._dataConverter = new DataConverter());
  }

  static get Consts(): Consts {
    return this._consts ?? (this._consts = new Consts());
  }
}

import DataManager from '../dataManager';

export default class CreditCard {
  readonly number: string;
  readonly expiryDate: string;
  readonly cvv: string;
  readonly holderName: string;
  readonly postalCode: string;

  constructor(
    number: string = DataManager.Consts.CREDIT_CARD_NUMBER,
    expiryDate: string = DataManager.Consts.CREDIT_CARD_EXPIRY_DATE,
    cvv: string = DataManager.Consts.CREDIT_CARD_CVV,
    holderName: string = DataManager.Consts.CREDIT_CARD_HOLDER_NAME,
    postalCode: string = 'RG25 2AR',
  ) {
    this.number = number;
    this.expiryDate = expiryDate;
    this.cvv = cvv;
    this.holderName = holderName;
    this.postalCode = postalCode;
  }

  toString(): string {
    return `${this.number} (${this.expiryDate})`;
  }
}

import { Page } from '@playwright/test';
import Package from '../entities/package';
import Site from '../entities/site';
import DropTypesEnum from '../enums/dropTypesEnum';
import { TestStep } from '../innerInfra/decorators';
import { DroppingUser } from '../innerInfra/interfaces/actorInterfaces/droppingUser';
import { Logger } from '../innerInfra/logger';
import IntercomModal from '../innerInfra/pages/intercomModal';
import BaseActor from './baseActor';

export abstract class DroppingUserActor extends BaseActor implements DroppingUser {
  protected _site: Site | undefined;
  readonly address: string;
  readonly dropType: DropTypesEnum;

  constructor(page: Page, dropType: DropTypesEnum, address: string, site?: Site) {
    super(page);

    if (!address) {
      throw new Error('Address is undefined or empty');
    }

    if (!dropType) {
      throw new Error('Drop type is undefined');
    }

    this._site = site;
    this.dropType = dropType;
    this.address = address;
  }

  get site(): Site {
    this.throwErrorIfSiteIsNotDefinedProperly();

    return this._site!;
  }

  override get perform() {
    return {
      ...super.perform,
      login: () => {
        Logger.error(
          'Login is not implemented for the dropping user abstract class, please use the child classes when trying to login',
        );
      },
      enterPackageCode: this.bindAction(this.enterPackageCode, this),
      enterVerificationCode: this.bindAction(this.enterVerificationCode, this),
    };
  }

  override get assert() {
    return {
      invalidVerificationCodes: this.bindAction(this.assertInvalidVerificationCodes, this),
      invalidPackageCodes: this.bindAction(this.assertInvalidPackageCodes, this),
    };
  }

  override get ask() {
    return {
      ...super.ask,
      whatIsTheVerificationCodeErrorMessage: this.bindAction(this.whatIsTheVerificationCodeErrorMessage, this),
      whatIsThePackageInputScanError: this.bindAction(this.whatIsThePackageInputScanError, this),
    };
  }

  override get goTo() {
    return {
      intercomModal: this.bindAction(this.openIntercomModal, this),
    };
  }

  protected throwErrorIfSiteIsNotDefinedProperly() {
    if (!this._site) {
      throw new Error('Site is undefined');
    }

    if (!this._site.stores.length) {
      throw new Error('Site does not have any stores');
    }
  }

  protected abstract enterVerificationCode({
    verificationCode,
    navigateToPageIfNeeded,
  }: {
    verificationCode: string;
    navigateToPageIfNeeded?: boolean;
  });

  protected abstract enterPackageCode(
    packagee: Package,
    { shouldPackageAdditionFail }: { shouldPackageAdditionFail: boolean },
  );

  protected abstract whatIsTheVerificationCodeErrorMessage();

  protected abstract whatIsThePackageInputScanError();

  @TestStep(`Assert invalid verification codes`)
  protected async assertInvalidVerificationCodes(
    invalidCodes: string[],
    assertion: (errorMessage: string, code: string) => void,
  ) {
    for (const code of invalidCodes) {
      await this.perform.enterVerificationCode({
        verificationCode: code,
        navigateToPageIfNeeded: false,
      });

      const errorMessage = await this.ask.whatIsTheVerificationCodeErrorMessage();
      assertion(errorMessage, code);
    }
  }

  @TestStep(`Assert invalid package codes`)
  protected async assertInvalidPackageCodes(
    invalidCodes: Package[],
    assertion: (errorMessage: string, code: string) => void,
  ) {
    for (const packagee of invalidCodes) {
      await this.perform.enterPackageCode(packagee, {
        shouldPackageAdditionFail: true,
      });

      const errorMessage = await this.ask.whatIsThePackageInputScanError();
      assertion(errorMessage, packagee.packageCode);
    }
  }

  protected abstract getHomePage();

  @TestStep(`Open Intercom Modal`)
  private async openIntercomModal({ shouldLogin = true }: { shouldLogin?: boolean } = {}) {
    const intercomAccessiblePage = this.getHomePage();

    if (shouldLogin) {
      await this.perform.login();
      await intercomAccessiblePage.intercomButton.locator.waitFor();
      await this.perform.refreshPage(intercomAccessiblePage);
    }

    await intercomAccessiblePage.intercomButton.click();

    const intercomPage = new IntercomModal(this.page);
    await intercomPage.init();
    await intercomPage.sendUsMessage.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: true,
      timeout: 10000,
      errorMessage: 'Failed to load intercom page, send us message button is not visible',
    });
  }
}

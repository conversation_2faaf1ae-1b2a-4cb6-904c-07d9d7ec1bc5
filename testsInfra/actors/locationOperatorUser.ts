import { Page } from '@playwright/test';
import DataManager from '../dataManager';
import StoresActionsLocalOperator from '../innerInfra/actorActions/adminActions/storesActionsLocalOperator';
import { Auth0LoginUser } from '../innerInfra/interfaces/actorInterfaces/auth0Login';
import BaseAdminUser from './baseAdminUser';

export default class LocationOperatorUser extends BaseAdminUser implements Auth0LoginUser {
  private readonly storeActions: StoresActionsLocalOperator;

  constructor(
    page: Page,
    email: string = DataManager.Consts.LOCAL_OPERATION_ADMIN_EMAIL,
    password: string = DataManager.Consts.LOCAL_OPERATION_ADMIN_PASSWORD,
  ) {
    super(page, email, password);

    this.storeActions = new StoresActionsLocalOperator(this);
  }

  override get perform() {
    return {
      ...super.perform,
      filterStoresByName: this.bindAction(this.storeActions.filterByName, this.storeActions),
    };
  }

  override get assert() {
    return {
      ...super.assert,
      storeCardNames: this.bindAction(this.storeActions.assertNameOfEveryStoreCard, this.storeActions),
      storeCardLocations: this.bindAction(this.storeActions.assertLocationOfEveryStoreCard, this.storeActions),
    };
  }

  override get goTo() {
    return {
      ...super.goTo,
      storesViewPage: this.bindAction(this.storeActions.goToStoresViewPage, this.storeActions),
    };
  }
}

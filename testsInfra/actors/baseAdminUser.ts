import { expect, Page } from '@playwright/test';
import DataManager from '../dataManager';
import { LoginType } from '../entities/loginType';
import Site from '../entities/site';
import DeliveryDetailsActions from '../innerInfra/actorActions/adminActions/deliveryDetailsActions';
import OperationHubActions from '../innerInfra/actorActions/adminActions/operationHubActions';
import IntercomActions from '../innerInfra/actorActions/intercomActions';
import LoginActions from '../innerInfra/actorActions/loginActions';
import { TestStep } from '../innerInfra/decorators';
import { Auth0LoginUser } from '../innerInfra/interfaces/actorInterfaces/auth0Login';
import DeliveriesPage from '../innerInfra/pages/businessPages/hfs/deliveriesPage';
import OperationHubPage from '../innerInfra/pages/businessPages/hfs/operationsHub';
import UpcomingPickupsPage from '../innerInfra/pages/businessPages/hfs/upcomingPickupsPage';
import NavigationBusinessPage from '../innerInfra/pages/businessPages/navigationBusinessPage';
import IntercomModal from '../innerInfra/pages/intercomModal';
import BaseActor from './baseActor';

export default class BaseAdminUser extends BaseActor implements Auth0LoginUser {
  readonly email: string;
  readonly password: string;
  readonly loginType: LoginType = DataManager.Consts.LoginTypes.Business;

  protected readonly operationHubActions: OperationHubActions;
  protected readonly deliveryDetailsActions: DeliveryDetailsActions;
  protected readonly loginActions: LoginActions<BaseAdminUser>;
  protected readonly intercomActions: IntercomActions;

  constructor(
    page: Page,
    email: string = DataManager.Consts.LOCAL_OPERATION_ADMIN_EMAIL,
    password: string = DataManager.Consts.LOCAL_OPERATION_ADMIN_PASSWORD,
  ) {
    if (!email || !password) {
      throw new Error('Email and password cannot be undefined');
    }

    super(page);

    this.email = email;
    this.password = password;
    this.operationHubActions = new OperationHubActions(this);
    this.deliveryDetailsActions = new DeliveryDetailsActions(this);
    this.loginActions = new LoginActions<BaseAdminUser>(this);
    this.intercomActions = new IntercomActions(this);
  }

  override get perform() {
    return {
      ...super.perform,
      login: this.bindAction(this.loginActions.login, this.loginActions),
      goToOperationsHubForTheSite: this.bindAction(this.goToOperationsHub, this),
      receivePackages: this.bindAction(this.operationHubActions.receivePackages, this.operationHubActions),
      handoverPackages: this.bindAction(this.operationHubActions.handoverDelivery, this.operationHubActions),
      searchPackage: this.bindAction(this.operationHubActions.searchPackage, this.operationHubActions),
      preparePackageForDispatch: this.bindAction(
        this.operationHubActions.prepareDeliveryForDispatch,
        this.operationHubActions,
      ),
      dispatchDelivery: this.bindAction(this.operationHubActions.dispatchDelivery, this.operationHubActions),
      loginAndReceivePackages: this.bindAction(
        this.operationHubActions.loginAndReceivePackages,
        this.operationHubActions,
      ),
      prepareDeliveryForDispatch: this.bindAction(
        this.operationHubActions.prepareDeliveryForDispatch,
        this.operationHubActions,
      ),
      prepareAndDispatchDelivery: this.bindAction(
        this.operationHubActions.prepareAndDispatchDelivery,
        this.operationHubActions,
      ),
      markDeliveryAsDelivered: this.bindAction(
        this.deliveryDetailsActions.markDeliveryAsDelivered,
        this.deliveryDetailsActions,
      ),
      sealPackage: this.bindAction(this.deliveryDetailsActions.sealPackage, this.deliveryDetailsActions),
      deliverPackages: this.bindAction(this.deliveryDetailsActions.deliverPackages, this.deliveryDetailsActions),
      startConversationWithIntercom: this.bindAction(
        this.intercomActions.startConversationWithIntercom,
        this.intercomActions,
      ),
      rebookDelivery: this.bindAction(this.deliveryDetailsActions.rebookDelivery, this.deliveryDetailsActions),
    };
  }

  override get ask() {
    return {
      ...super.ask,
      whatIsTheLoginErrorMessage: this.bindAction(this.loginActions.whatIsTheLoginErrorMessage, this.loginActions),
      getErrorPopupMessageForEmptyField: this.bindAction(
        this.loginActions.getErrorPopupMessageForEmptyField,
        this.loginActions,
      ),
      whatIsTheDeliveryStatus: this.bindAction(
        this.deliveryDetailsActions.whatIsTheDeliveryStatus,
        this.deliveryDetailsActions,
      ),
    };
  }

  override get assert() {
    return {
      invalidLoginInputs: this.bindAction(this.loginActions.assertInvalidLoginInputs, this.loginActions),
      deliveryStatus: this.bindAction(this.deliveryDetailsActions.assertDeliveryStatus, this.deliveryDetailsActions),
      intercomConversation: this.bindAction(this.intercomActions.assertIntercomConversation, this.intercomActions),
      courierStatus: this.bindAction(this.deliveryDetailsActions.assertCourierStatus, this.deliveryDetailsActions),
      menuOptions: this.bindAction(this.assertMenuOptions, this),
    };
  }

  override get goTo() {
    return {
      loginPage: this.bindAction(this.loginActions.goToLoginPage, this.loginActions),
      intercomModal: this.bindAction(this.openIntercomModal, this),
      operationsHubPage: this.bindAction(this.goToOperationsHub, this),
      pickupsPage: this.bindAction(this.goToPickupsPage, this),
      deliveriesPage: this.bindAction(this.goToDeliveriesPage, this),
    };
  }

  @TestStep('Go to pickups page')
  private async goToPickupsPage({
    shouldNavigateToPage = true,
    shouldNavigateThroughUI = true,
  }: { shouldNavigateToPage?: boolean; shouldNavigateThroughUI?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);
    const upcomingStorePackagesResponse = this.page.waitForResponse(
      (response) => response.url().includes('/upcoming-store-packages/') && response.status() === 200,
    );

    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage,
      page: navigationBusinessPage,
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        await navigationBusinessPage.pickupsButton.click();
      },
    });

    const pickupsPage = new UpcomingPickupsPage(this.page);
    await pickupsPage.storeFilter.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load pickups page, search bar is not visible',
    });

    const response = await upcomingStorePackagesResponse;
    expect(response.status(), 'Failed to load pickups, response status is not 200').toBe(200);

    return pickupsPage;
  }

  @TestStep('Go to deliveries page')
  private async goToDeliveriesPage({
    shouldNavigateToPage = true,
    shouldNavigateThroughUI = true,
  }: { shouldNavigateToPage?: boolean; shouldNavigateThroughUI?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);
    const upcomingStorePackagesResponse = this.page.waitForResponse(
      (response) => response.url().includes('/upcoming-deliveries/') && response.status() === 200,
    );

    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage,
      page: navigationBusinessPage,
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        await navigationBusinessPage.deliveriesButton.click();
      },
    });

    const deliveriesPage = new DeliveriesPage(this.page);
    await deliveriesPage.deliveryIdFilter.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load deliveries page, search bar is not visible',
    });

    const response = await upcomingStorePackagesResponse;
    expect(response.status(), 'Failed to load deliveries, response status is not 200').toBe(200);

    return deliveriesPage;
  }

  @TestStep(`Go to operations hub for the base Site`)
  private async goToOperationsHub({
    site,
    afterLogin = false,
    shouldNavigateToPage = false,
  }: {
    site: Site;
    afterLogin: boolean;
    shouldNavigateToPage?: boolean;
  }) {
    const navigationBusinessPage = new NavigationBusinessPage(this._page);

    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage: shouldNavigateToPage,
      page: navigationBusinessPage,
      shouldWaitForLoad: afterLogin,
    });

    if (afterLogin) {
      await navigationBusinessPage.salesSummaryCard.waitToBeVisible();
    }

    await navigationBusinessPage.operationsHubSidebarButton.click();

    const operationsHubPage = new OperationHubPage(this.page, site);
    await operationsHubPage.searchPackageInput.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load operations hub page, search input is not visible',
    });
  }

  @TestStep(`Open Intercom Modal`)
  private async openIntercomModal({ shouldLogin = true }: { shouldLogin?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);

    if (shouldLogin) {
      await this.perform.login();
      await navigationBusinessPage.intercomButton.locator.waitFor();
    }

    await navigationBusinessPage.intercomButton.click();
    const intercomPage = new IntercomModal(this.page);

    await intercomPage.init();
    await intercomPage.sendUsMessage.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load intercom modal, send us message button is not visible',
    });
  }

  @TestStep('Assert Menu Options')
  private async assertMenuOptions({ assertion }: { assertion: (siteName: string) => void }) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);
    await navigationBusinessPage.pickupsButton.waitToBeVisible({ shouldThrowSoftError: true });
    await navigationBusinessPage.deliveriesButton.waitToBeVisible({ shouldThrowSoftError: true });
    await navigationBusinessPage.storesViewButton.waitToBeVisible({ shouldThrowSoftError: true });
    await navigationBusinessPage.vendorsViewButton.waitToBeInvisible({ shouldThrowSoftError: true });

    await navigationBusinessPage.switchSiteContextButton.click();
    await navigationBusinessPage.siteSelectionDropdownList.waitToBeInvisible({
      timeout: 5000,
      shouldThrowSoftError: true,
      errorMessage: 'Site Selection List is visible despite only one site being shown to this user',
    });

    assertion(await navigationBusinessPage.switchSiteContextButton.textContent());
  }
}

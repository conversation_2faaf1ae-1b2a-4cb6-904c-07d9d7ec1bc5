import { Page } from '@playwright/test';
import DataManager from '../dataManager';
import Site from '../entities/site';
import StoresActionsGlobalAdmin from '../innerInfra/actorActions/adminActions/storesActionsGlobalAdmin';
import { TestStep } from '../innerInfra/decorators';
import OperationHubPage from '../innerInfra/pages/businessPages/hfs/operationsHub';
import VendorDetailsPage from '../innerInfra/pages/businessPages/hfs/vendorDetailsPage';
import VendorsViewPage from '../innerInfra/pages/businessPages/hfs/vendorsViewPage';
import NavigationBusinessPage from '../innerInfra/pages/businessPages/navigationBusinessPage';
import LocationOperatorUser from './locationOperatorUser';

export default class GlobalAdmin extends LocationOperatorUser {
  constructor(
    page: Page,
    email: string = DataManager.Consts.BUSINESS_GLOBAL_ADMIN_EMAIL,
    password: string = DataManager.Consts.BUSINESS_GLOBAL_ADMIN_PASSWORD,
  ) {
    if (!email || !password) {
      throw new Error('Email and password cannot be undefined');
    }

    super(page, email, password);
  }

  protected override get storeActions() {
    if (!this._storesActions) {
      this._storesActions = new StoresActionsGlobalAdmin(this);
    }

    return this._storesActions;
  }

  override get perform() {
    return {
      ...super.perform,
      goToOperationsHubForTheSite: this.bindAction(this.goToOperationsHubForTheSite, this),
    };
  }

  override get goTo() {
    return {
      ...super.goTo,
      operationsHubPage: this.bindAction(this.goToOperationsHubForTheSite, this),
      vendorsViewPage: this.bindAction(this.goToVendorsViewPage, this),
    };
  }

  @TestStep('Go to vendors view page')
  private async goToVendorsViewPage({
    shouldNavigateToPage = false,
    viewVendorDetails = false,
  }: { shouldNavigateToPage?: boolean; viewVendorDetails?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.page);
    await this.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: navigationBusinessPage });
    await navigationBusinessPage.vendorsViewButton.click();

    const vendorsViewPage = new VendorsViewPage(this.page);
    await vendorsViewPage.searchBar.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load vendors view page, vendor search bar is not visible',
    });

    if (viewVendorDetails) {
      await this.openVendorDetailsPage({ vendorsViewPage });
    }
  }

  @TestStep(`Open First Vendor Details Page`)
  private async openVendorDetailsPage({
    vendorsViewPage = new VendorsViewPage(this.page),
  }: { vendorsViewPage?: VendorsViewPage } = {}) {
    //TODO: change to:
    // await vendorsViewPage.vendorsCards.firstResult();
    // Once backend fixes the config
    const firstVendorResult = await vendorsViewPage.vendorsCards.getByIndex({
      index: 1,
      exactMatch: false,
      timeout: 5000,
    });
    await firstVendorResult.click();

    const vendorDetailsPage = new VendorDetailsPage(this.page);
    await vendorDetailsPage.vendorDetailsContent.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load vendor details page, vendor content is not visible',
    });

    const firstConfigCard = await vendorDetailsPage.configCards.firstResult();
    await firstConfigCard.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load vendor details page, config cards are not visible',
    });
  }

  @TestStep(`Go to operations hub for the selected Site`)
  private async goToOperationsHubForTheSite({
    site,
    afterLogin = false,
    shouldNavigateToPage = false,
  }: {
    site: Site;
    afterLogin: boolean;
    shouldNavigateToPage?: boolean;
  }) {
    const navigationBusinessPage = new NavigationBusinessPage(this._page);

    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage: shouldNavigateToPage,
      page: navigationBusinessPage,
      shouldWaitForLoad: afterLogin,
    });

    if (afterLogin) {
      await navigationBusinessPage.salesSummaryCard.waitToBeVisible();
    }

    await navigationBusinessPage.switchSiteContextButton.click();

    // After login the page might refresh unexpectedly, so if the element isn't visible after click it means we need to do it again
    // if we fail to make the element appear after 5 seconds and additional click, it means there is a bug
    if (await navigationBusinessPage.siteSelectionDropdownList.waitToBeVisible({ timeout: 5000 })) {
      await navigationBusinessPage.siteSelectionDropdownList.selectOption({ optionToSelect: site.name });
    } else {
      await navigationBusinessPage.switchSiteContextButton.click();
      await navigationBusinessPage.siteSelectionDropdownList.selectOption({ optionToSelect: site.name });
    }

    await navigationBusinessPage.operationsHubSidebarButton.click();

    const operationsHubPage = new OperationHubPage(this.page, site);
    await operationsHubPage.searchPackageInput.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load operations hub page, search input is not visible',
    });
  }
}

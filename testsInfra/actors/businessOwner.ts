import { expect, Page } from '@playwright/test';
import DataManager from '../dataManager';
import CreditCard from '../entities/creditCard';
import { LoginType } from '../entities/loginType';
import Package from '../entities/package';
import Site from '../entities/site';
import DropTypesEnum from '../enums/dropTypesEnum';
import FulfillmentMenuOptionsEnum from '../enums/fulfillmentMenuOptionsEnum';
import BusinessOwnerCheckoutActions from '../innerInfra/actorActions/businessOwnerActions/businessOwnerCheckoutActions';
import DispatchActions from '../innerInfra/actorActions/businessOwnerActions/dispatchActions';
import TrackerActions from '../innerInfra/actorActions/businessOwnerActions/trackerActions';
import LoginActions from '../innerInfra/actorActions/loginActions';
import { TestStep } from '../innerInfra/decorators';
import { Auth0LoginUser } from '../innerInfra/interfaces/auth0Login';
import BaseFulfillmentPage from '../innerInfra/pages/fulfillmentPages/baseFulfillmentPage';
import HomeFulfillmentPage from '../innerInfra/pages/fulfillmentPages/homeFulfillmentPage';
import { DroppingUserActor } from './droppingUser';

export class BusinessOwnerUser extends DroppingUserActor implements Auth0LoginUser {
  readonly phoneNumber: string;
  readonly email: string;
  readonly password: string;
  readonly loginType: LoginType = DataManager.Consts.LoginTypes.Fulfillment;

  private readonly loginActions: LoginActions<BusinessOwnerUser>;
  private readonly deliveryActions: BusinessOwnerCheckoutActions;
  private readonly trackerActions: TrackerActions;
  private readonly dispatchActions: DispatchActions;

  constructor(
    page: Page,
    site?: Site,
    options?: {
      address?: string;
      dropType?: DropTypesEnum;
      packages?: Package[];
      creditCard?: CreditCard;
      email?: string;
      password?: string;
    },
  ) {
    super(page, options?.dropType ?? DropTypesEnum.Delivery, options?.address ?? '123', site);
    this.email = options?.email ?? DataManager.Consts.BUSINESS_GLOBAL_ADMIN_EMAIL;
    this.password = options?.password ?? DataManager.Consts.BUSINESS_GLOBAL_ADMIN_PASSWORD;
    this.loginActions = new LoginActions<BusinessOwnerUser>(this);
    this.deliveryActions = new BusinessOwnerCheckoutActions(this);
    this.trackerActions = new TrackerActions(this);
    this.dispatchActions = new DispatchActions(this);
  }

  override get perform() {
    return {
      ...super.perform,
      addPackages: this.bindAction(this.deliveryActions.addPackages, this.deliveryActions),
      addReceiptsManually: this.bindAction(this.deliveryActions.addReceiptsManually, this.deliveryActions),
      checkout: this.bindAction(this.deliveryActions.checkout, this.deliveryActions),
      addAddress: this.bindAction(this.deliveryActions.addAddress, this.deliveryActions),
      selectDropitPass: this.bindAction(this.deliveryActions.selectDropitPass, this.deliveryActions),
      login: this.bindAction(this.loginActions.login, this.loginActions),
      dropPackages: this.bindAction(this.deliveryActions.dropPackages, this.deliveryActions),
      droppingAPackageForConsumer: this.bindAction(this.droppingAPackageForConsumer, this),
      dispatchPackages: this.bindAction(this.dispatchActions.dispatchPackages, this.dispatchActions),
      showFulfillmentUI: this.bindAction(this.showFulfillmentUI, this),
      inputVerificationCode: this.bindAction(this.inputVerificationCode, this),
    };
  }

  override get ask() {
    return {
      ...super.ask,
      whatIsTheLoginErrorMessage: this.bindAction(this.loginActions.whatIsTheLoginErrorMessage, this.loginActions),
      getErrorPopupMessageForEmptyField: this.bindAction(
        this.loginActions.getErrorPopupMessageForEmptyField,
        this.loginActions,
      ),
      whatIsThePackageInputScanError: this.bindAction(this.whatIsThePackageInputScanError, this),
    };
  }

  override get assert() {
    return {
      ...super.assert,
      invalidLoginInputs: this.bindAction(this.loginActions.assertInvalidLoginInputs, this.loginActions),
      deliveryStatus: this.bindAction(this.trackerActions.assertDeliveryStatus, this.trackerActions),
    };
  }

  override get goTo() {
    return {
      ...super.goTo,
      loginPage: this.bindAction(this.loginActions.goToLoginPage, this.loginActions),
      dispatchPage: this.bindAction(this.dispatchActions.goToDispatchPage, this.dispatchActions),
      trackOrdersPage: this.bindAction(this.trackerActions.goToTrackOrdersPage, this.trackerActions),
      deliveryPage: this.bindAction(this.deliveryActions.goToDeliveryPage, this.deliveryActions),
      homePage: this.bindAction(this.goToHomePage, this),
    };
  }

  protected getHomePage() {
    return new HomeFulfillmentPage(this.page);
  }

  @TestStep(`Go to home page`)
  private async goToHomePage({ shouldNavigateWithClick = true }: { shouldNavigateWithClick?: boolean } = {}) {
    const basePage = new BaseFulfillmentPage(this.page);

    await this.perform.navigateToPageIfNeeded({
      shouldNavigateToPage: true,
      page: basePage,
      shouldUseCustomNavigation: shouldNavigateWithClick,
      customNavigation: async () => {
        await basePage.selectMenuOption(FulfillmentMenuOptionsEnum.Home);
      },
    });

    const homePage = new HomeFulfillmentPage(this.page);
    await homePage.newDeliveryButton.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: false,
      timeout: 5000,
      errorMessage: 'Failed to load home page, new delivery button is not visible',
    });
  }

  @TestStep(`Enter Verification code`)
  protected async enterVerificationCode({
    verificationCode,
    navigateToPageIfNeeded,
  }: {
    verificationCode: string;
    navigateToPageIfNeeded?: boolean;
  }) {
    if (navigateToPageIfNeeded) {
      await this.perform.login();
      await this.showFulfillmentUI({ storeCode: verificationCode });
    }

    await this.inputVerificationCode({ verificationCode });
  }

  @TestStep(`Get verification code error message`)
  protected async whatIsTheVerificationCodeErrorMessage() {
    const homePage = new HomeFulfillmentPage(this.page);

    return {
      title: await homePage.verificationCodeTitle.textContent(),
      errorMessage: await homePage.verificationCodeErrorMessage.textContent(),
    };
  }

  @TestStep(`Assert invalid verification codes`)
  protected async assertInvalidVerificationCodes(
    invalidCodes: string[],
    assertion: (errorMessage: string, code: string) => void,
  ): Promise<void> {
    for (const code of invalidCodes) {
      await this.perform.enterVerificationCode({
        verificationCode: code,
        navigateToPageIfNeeded: false,
      });

      const errorMessageAndTitle = await this.ask.whatIsTheVerificationCodeErrorMessage();

      expect.soft(errorMessageAndTitle.title, 'Asserting verification code title').toContain('Wrong code!');
      assertion(errorMessageAndTitle.errorMessage, code);
    }
  }

  @TestStep(`Enter package code`)
  protected override async enterPackageCode(
    packagee: Package,
    { shouldPackageAdditionFail }: { shouldPackageAdditionFail: boolean },
  ) {
    await this.perform.addPackages({
      additionalPackages: [packagee],
      shouldBarcodeInputErrorAppear: shouldPackageAdditionFail,
      shouldNavigateToPage: false,
    });
  }

  @TestStep(`Show Business Owner UI`)
  private async showFulfillmentUI({
    storeCode = '4242',
    shouldInputCode = true,
  }: { storeCode?: string; shouldInputCode?: boolean } = {}) {
    const homePage = new HomeFulfillmentPage(this.page);

    await homePage.businessNameButton.click();

    if (shouldInputCode) {
      await this.inputVerificationCode({ verificationCode: storeCode, homePage });
    }
  }

  @TestStep(`Input store verification code`)
  private async inputVerificationCode({
    verificationCode,
    homePage = new HomeFulfillmentPage(this.page),
  }: {
    verificationCode: string;
    homePage?: HomeFulfillmentPage;
  }) {
    homePage ??= new HomeFulfillmentPage(this.page);

    await homePage.storeLoginStoreCodeInput.fill(verificationCode);
  }

  @TestStep(`Existing Store Owner sends consumer packages to delivery`)
  private async droppingAPackageForConsumer({
    money = '50',
    additionalContactInfo = 'Bizi drop',
    shouldLogin = true,
    packages = [],
    firstName = 'Arya',
    lastName = 'Stark',
    phoneNumber = DataManager.Consts.PHONE_NUMBER2,
    country = 'Israel',
    shouldPackageAdditionSucceed = true,
  }: {
    money?: string;
    additionalContactInfo?: string;
    shouldLogin?: boolean;
    packages?: Package[];
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    country?: string;
    shouldPackageAdditionSucceed?: boolean;
  } = {}) {
    if (shouldLogin) {
      await this.perform.login();
    }

    await this.showFulfillmentUI();

    const addedPackages = await this.perform.dropPackages({
      shouldAllPackageAdditionSucceed: shouldPackageAdditionSucceed,
      packages,
      money,
      additionalContactInfo,
      firstName,
      lastName,
      phoneNumber,
      country,
    });

    return addedPackages;
  }

  @TestStep(`Ask what is the package input scan error`)
  protected whatIsThePackageInputScanError() {
    return this.deliveryActions.whatIsThePackageInputScanError();
  }
}

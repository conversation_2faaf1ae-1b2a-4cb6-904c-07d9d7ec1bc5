import axios, { AxiosInstance } from 'axios';
import DataManager from '../../dataManager';
import { Logger } from '../logger';

type ServerData = {
  env: string;
  timeout: number;
  poopServices: Record<string, unknown>;
  global: Record<string, string>;
  sites: Record<string, Record<string, string>>;
};

// We will use axios only for the health check api,
// This is to allow to prevent running the tests before they are initialized
export default class HealthCheckApi {
  protected request: AxiosInstance;
  private readonly TIMEOUT_MS = 3000;
  private controller: AbortController;
  private timeout: NodeJS.Timeout;

  constructor() {
    this.request = axios.create();
    this.controller = new AbortController();
  }

  private setupTimeout() {
    this.controller = new AbortController();
    this.timeout = setTimeout(() => this.controller.abort(), this.TIMEOUT_MS);
  }

  private clearTimeout() {
    clearTimeout(this.timeout);
  }

  async retryRequest<T>(
    func: () => Promise<T>,
    retries: number = 3,
    delay: number = 2000,
    maxTotalTime: number = 15000,
  ) {
    let error: Error = new Error('Failed to retry request');
    const start = Date.now();

    for (let i = 0; i < retries; i++) {
      const elapsed = Date.now() - start;
      if (elapsed > maxTotalTime) {
        error = new Error(`Retry limit exceeded (${maxTotalTime}ms total)`);
        break;
      }

      try {
        return await func();
      } catch (err) {
        error = err as Error;
        Logger.info(`Retrying request, attempt ${i + 1} of ${retries}`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw error;
  }

  async getHealthCheck(): Promise<ServerData> {
    return await this.retryRequest(async () => {
      this.setupTimeout();
      let responseData: ServerData | undefined = undefined;

      try {
        const response = await axios.get(
          `https://werner.${DataManager.Consts.TESTING_ENV}.drpt.io/api/health-check/all/?timeout=2`,
          {
            signal: this.controller.signal,
          },
        );

        responseData = response.data as ServerData;
        if (response.status !== 200) {
          Logger.debug(`Health Check response: ${JSON.stringify(responseData, null, 2)}`);
        }

        return responseData;
      } catch (error) {
        if (error.code === 'ERR_CANCELED') {
          throw new Error(
            `Aborted Health Check after ${this.TIMEOUT_MS}ms, this is most likely a 522 error.
            \nMake sure the environment is up and setup correctly
            \nResponse: ${responseData ? JSON.stringify(responseData) : 'None'}`,
          );
        }
        throw error;
      } finally {
        this.clearTimeout();
      }
    });
  }

  async getEnvironmentStatus() {
    return await this.retryRequest(async () => {
      this.setupTimeout();

      try {
        const response = await axios.get(`https://api-v2.${DataManager.Consts.TESTING_ENV}.drpt.io/status`, {
          signal: this.controller.signal,
        });

        return response.status;
      } catch (error) {
        if (error.code === 'ERR_CANCELED') {
          throw new Error(
            `Aborted Environment Status check after ${this.TIMEOUT_MS}ms, this is most likely a 522 error.\nMake sure the environment is up and setup correctly`,
          );
        }

        throw error;
      } finally {
        this.clearTimeout();
      }
    });
  }

  async verifyAllServicesAreUp() {
    const environmentStatus = await this.getEnvironmentStatus();

    if (environmentStatus !== 200) {
      throw new Error(`Environment is not up properly, status code: ${environmentStatus}`);
    }

    const healthCheckData: ServerData = await this.getHealthCheck();
    const downServers: string[] = [];

    for (const [server, status] of Object.entries(healthCheckData.global)) {
      if (!status.includes('😎')) {
        downServers.push(server);
      }
    }

    for (const [site, servers] of Object.entries(healthCheckData.sites)) {
      for (const [server, status] of Object.entries(servers)) {
        if (!status.includes('😎')) {
          downServers.push(`${site} - ${server}`);
        }
      }
    }

    downServers.push(...Object.keys(healthCheckData.poopServices));

    if (downServers.length > 0) {
      throw new Error(`The following servers are down: ${downServers.join(', ')}`);
    }

    Logger.info(`Health Check completed successfully`);
  }
}

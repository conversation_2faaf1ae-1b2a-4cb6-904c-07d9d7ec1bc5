import ViewportTypeEnum from '../../enums/viewPortTypeEnum';

export default class DataConverter {
  convertSiteNameToLocatorString = (siteName: string) => siteName?.toLowerCase().replace(/\s/g, '-');

  convertNumberToPaymentString = ({
    amount,
    currencyCode,
    languageLocal,
  }: {
    amount: number;
    currencyCode: string;
    languageLocal: string;
  }) => {
    const formatter = new Intl.NumberFormat(languageLocal, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    return formatter.format(amount);
  };

  convertViewPortToViewPortType(viewPort: { width: number; height: number } | null): ViewportTypeEnum {
    if (!viewPort) {
      throw new Error('Viewport is undefined, make sure the page is open');
    }

    const maxWidths = {
      [ViewportTypeEnum.Mobile]: 480,
      [ViewportTypeEnum.Tablet]: 1024,
      [ViewportTypeEnum.PC]: 3840,
    };

    const width = viewPort.width;

    if (width <= maxWidths[ViewportTypeEnum.Mobile]) {
      return ViewportTypeEnum.Mobile;
    }

    if (width <= maxWidths[ViewportTypeEnum.Tablet]) {
      return ViewportTypeEnum.Tablet;
    }

    if (width <= maxWidths[ViewportTypeEnum.PC]) {
      return ViewportTypeEnum.PC;
    }

    throw new Error(`Unsupported viewport width: ${width}`);
  }

  convertTextToNumber = (text: string): number => {
    const parsedNumberString = text.match(/[\d,]+/)?.[0]?.replace(',', '.');
    if (!parsedNumberString) {
      throw new Error('Failed to parse number from the given text: ' + text);
    }

    return parseFloat(parsedNumberString);
  };

  convertToTestTitleName = ({
    titleText,
    ticketNumber,
    additionalTickets = [],
  }: {
    titleText: string;
    ticketNumber: number;
    additionalTickets?: number[];
  }): string => {
    return `[T${ticketNumber}]${additionalTickets.length > 0 ? ' ' + additionalTickets.map((t) => `[T${t}]`).join(' ') : ''} ${titleText}`;
  };
}

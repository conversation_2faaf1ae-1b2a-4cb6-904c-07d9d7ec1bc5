import test from '@playwright/test';
import DataManager from '../dataManager';

export class Logger {
  private static readonly AUTOMATION_SIGNATURE = '[arya-stark]';

  static AnnotationInfo(message: string) {
    test.info().annotations.push({ type: 'info', description: message });
  }

  static AnnotationWarning(message: string) {
    test.info().annotations.push({ type: 'warning', description: message });
  }

  static AnnotationError(message: string) {
    test.info().annotations.push({ type: 'error', description: message });
  }

  static debug(message: string, dataToAnnotate?: string) {
    if (DataManager.Consts.RUNNING_ENV === DataManager.Consts.DEBUG_ENV) {
      console.debug(`${this.AUTOMATION_SIGNATURE}[DEBUG] ${message}`);

      if (dataToAnnotate) {
        this.AnnotationInfo(dataToAnnotate);
      }
    }
  }

  static step(message: string, dataToAnnotate?: string) {
    console.info(`${this.AUTOMATION_SIGNATURE} ${message}`);

    if (dataToAnnotate) {
      this.AnnotationInfo(dataToAnnotate);
    }
  }

  static info(message: string, dataToAnnotate?: string) {
    console.info(`${this.AUTOMATION_SIGNATURE}[INFO] ${message}`);

    if (dataToAnnotate) {
      this.AnnotationInfo(dataToAnnotate);
    }
  }

  static warning(message: string, dataToAnnotate?: string) {
    console.warn(`${this.AUTOMATION_SIGNATURE}[WARNING] ${message}`);

    if (dataToAnnotate) {
      this.AnnotationWarning(dataToAnnotate);
    }
  }

  static error(message: string, dataToAnnotate?: string) {
    console.error(`${this.AUTOMATION_SIGNATURE}[ERROR] ${message}`);

    if (dataToAnnotate) {
      this.AnnotationError(dataToAnnotate);
    }
  }
}

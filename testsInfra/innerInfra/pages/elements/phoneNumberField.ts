import { Locator, Page } from '@playwright/test';
import Button from './button';
import TextArea from './textArea';
import VisualCue from './visualCue';

export default class PhoneNumberField extends TextArea {
  readonly countryPicker: Button;
  readonly countryDropdownInput: TextArea;
  readonly selectedCountry: (country: string) => Button;
  readonly errorLabel: VisualCue;

  constructor(
    page: Page,
    phoneInput: Locator,
    countryPicker: Locator,
    countryDropdownInput: Locator,
    errorLocator: Locator,
  ) {
    super(phoneInput);

    this.countryPicker = new Button(countryPicker);
    this.countryDropdownInput = new TextArea(countryDropdownInput);
    this.selectedCountry = (country: string) => new Button(page.getByText(country).first());
    this.errorLabel = new VisualCue(errorLocator);
  }

  override async fill(value: string) {
    this.fillWithCountryCode(value, 'Israel');
  }

  async fillWithCountryCode(phoneNumber: string, country: string) {
    await this.countryPicker.click();
    await this.countryDropdownInput.fill(country);
    await this.selectedCountry(country).click();

    await this.locator.fill(phoneNumber);
  }
}

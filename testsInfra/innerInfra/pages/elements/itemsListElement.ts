import { expect, Locator } from '@playwright/test';
import { Logger } from '../../logger';
import BaseElement from './baseElement';

interface ConstructableElement<T> {
  new (locator: Locator): T;
}

export default class ItemsListElement<T extends BaseElement = BaseElement> {
  public readonly locator: Locator;
  private readonly ComponentClass: ConstructableElement<T>;

  constructor(locator: Locator, componentClass: ConstructableElement<T>) {
    this.locator = locator;
    this.ComponentClass = componentClass;
  }

  async waitUntilResultsCount({
    expectedCount,
    exactMatch = false,
    timeout = 10000,
    shouldThrowError = true,
  }: {
    expectedCount: number;
    exactMatch?: boolean;
    timeout?: number;
    shouldThrowError?: boolean;
  }) {
    const errorMessage = `Expected ${exactMatch ? 'exactly' : 'at least'} ${expectedCount} results, but got ${await this.locator.count()}`;

    try {
      Logger.debug(`Waiting for ${expectedCount} results`);
      if (exactMatch) {
        await expect(this.locator).toHaveCount(expectedCount, { timeout });
      } else {
        await this.locator.nth(expectedCount - 1).waitFor({ state: 'visible', timeout });
      }

      return true;
    } catch (error) {
      if (!shouldThrowError) {
        Logger.debug(errorMessage);

        return false;
      }

      Logger.error(errorMessage);
      throw error;
    }
  }

  async firstResult({ timeout = 5000 } = {}): Promise<T> {
    const firstResult = await this.getByIndex({ index: 0, exactMatch: false, timeout });
    const firstResultLocator = firstResult.locator;

    Logger.debug(`First result locator: ${firstResultLocator}`);

    return firstResult;
  }

  async getByIndex({ index, exactMatch = true, timeout = 5000 }): Promise<T> {
    await this.waitUntilResultsCount({ expectedCount: index + 1, exactMatch, timeout });

    const results = this.locator.nth(index);

    return new this.ComponentClass(results);
  }

  async waitForResultsToBeDifferentThan({
    previousResultsCount,
    timeout = 5000,
  }: {
    previousResultsCount: number;
    timeout?: number;
  }) {
    await expect
      .poll(() => this.locator.count(), {
        timeout: timeout,
        message: `Waiting for the filtered list to update from ${previousResultsCount} items`,
      })
      .not.toBe(previousResultsCount);
  }

  async getAllResults() {
    const results = await this.locator.all();

    return results.map((result) => new this.ComponentClass(result));
  }
}

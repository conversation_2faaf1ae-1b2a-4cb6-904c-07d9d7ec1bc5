import { Locator } from '@playwright/test';
import Button from './button';
import TextArea from './textArea';

export default class DropdownList extends TextArea {
  private readonly _options: Locator;

  constructor(locator: Locator, allOptions: Locator) {
    super(locator);
    this._options = allOptions;
  }

  async getOptions({ shouldOpenOptions = false }: { shouldOpenOptions?: boolean } = {}) {
    if (shouldOpenOptions) {
      await this.click();
    }

    const options = await this._options.all();

    return options.map((option) => new Button(option));
  }

  async selectFirstOption() {
    const options = await this.getOptions({ shouldOpenOptions: true });

    await options[0].click();
  }

  async selectOption({
    optionToSelect,
    exactMatch = false,
    timeoutAfterAction = 0,
    shouldThrowError = true,
  }: {
    optionToSelect: string;
    exactMatch?: boolean;
    timeoutAfterAction?: number;
    shouldThrowError?: boolean;
  }) {
    await this.click();

    return await this.clickOnSelectedOption({ optionToSelect, exactMatch, timeoutAfterAction, shouldThrowError });
  }

  protected async clickOnSelectedOption({
    optionToSelect,
    exactMatch = false,
    timeoutAfterAction = 0,
    shouldThrowError = true,
  }: {
    optionToSelect: string;
    exactMatch?: boolean;
    timeoutAfterAction?: number;
    shouldThrowError?: boolean;
  }) {
    if (timeoutAfterAction > 0) {
      // While this is more flakey than other options,
      // In the case of a dropdown list that uses 3rd party apis for it's options(like google address search)
      // it's the only way to wait in order for it to populate the options
      // Any playwright action that will happen after the fill, will interrupt the request(what the hell google)
      // and will cause the requests to fail and the options to not populate
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await this.locator.page().waitForTimeout(timeoutAfterAction);
    }

    const options = await this.getOptions();

    for (const option of options) {
      const text = await option.textContent({ shouldClick: false });
      if (exactMatch ? text === optionToSelect : text?.includes(optionToSelect)) {
        await option.click();

        if (timeoutAfterAction > 0) {
          // eslint-disable-next-line playwright/no-wait-for-timeout
          await this.locator.page().waitForTimeout(timeoutAfterAction);
        }

        return true;
      }
    }

    if (shouldThrowError) {
      throw new Error(`Option "${optionToSelect}" not found`);
    }

    return false;
  }
}

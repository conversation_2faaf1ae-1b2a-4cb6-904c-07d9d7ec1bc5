import { Locator } from '@playwright/test';
import BaseElement from './baseElement';

export default class <PERSON><PERSON> extends BaseElement {
  constructor(locator: Locator) {
    super(locator);
  }

  async isDisabled() {
    const isLocatorDisabled = await this.locator.isDisabled();
    const isElementAriaDisabled = (await this.locator.getAttribute('aria-disabled')) === 'true';

    return isLocatorDisabled || isElementAriaDisabled;
  }
}

import { Locator } from '@playwright/test';
import DropdownList from './dropDownlist';

export default class DropdownListWithSearch extends DropdownList {
  constructor(locator: Locator, allOptions: Locator) {
    super(locator, allOptions);
  }

  override async selectOption({ optionToSelect, exactMatch = false, timeoutAfterAction = 0, shouldThrowError = true }) {
    await this.fill(optionToSelect);

    return await this.clickOnSelectedOption({ optionToSelect, exactMatch, timeoutAfterAction, shouldThrowError });
  }
}

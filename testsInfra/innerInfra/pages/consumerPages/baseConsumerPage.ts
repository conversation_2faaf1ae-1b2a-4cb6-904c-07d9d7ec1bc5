import { Page } from '@playwright/test';
import DataManager from '../../../dataManager';
import BasePage from '../basePage';
import Button from '../elements/button';

export default class BaseConsumerPage extends BasePage {
  readonly intercomButton: Button;
  readonly menuButton: Button;
  readonly settingsButton: Button;
  readonly logoutButton: Button;
  readonly trackOrdersButton: Button;
  readonly homeButton: Button;

  constructor(page: Page, url?: string) {
    super(page, url);

    this.intercomButton = new Button(page.getByTestId('AppHeader_intercom-button'));
    this.menuButton = new Button(page.getByTestId('AppHeader_menu-button'));
    this.homeButton = new Button(page.getByTestId('SideMenuButton_Home'));
    this.settingsButton = new Button(page.getByTestId('SideMenuButton_Settings'));
    this.logoutButton = new Button(page.getByTestId('LogoutTab_LogoutIcon_Pressable'));
    this.trackOrdersButton = new Button(page.getByTestId('SideMenuButton_Track-Orders'));
  }

  protected override getUrlPattern(): RegExp {
    return /drpt.io/;
  }

  public override getInitialUrl() {
    return `https://consumer-app.${DataManager.Consts.TESTING_ENV}.drpt.io`;
  }

  async removeIntercom() {
    await this.page.evaluate(() => {
      document.querySelector('#intercom-container')?.remove();
      document.querySelector('#intercom-frame')?.remove();
      document.querySelector('#intercom-css')?.remove();
    });
  }
}

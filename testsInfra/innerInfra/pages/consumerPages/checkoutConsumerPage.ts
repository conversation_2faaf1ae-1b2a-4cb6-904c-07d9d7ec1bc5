import { Page } from '@playwright/test';
import DataManager from '../../../dataManager';
import { DeliveryDetailsInConsumerApp } from '../../interfaces/deliveryDetailsInConsumerApp';
import BaseCheckoutPage from '../checkoutPages/baseCheckoutPage';
import Button from '../elements/button';
import DropdownList from '../elements/dropDownlist';
import ItemsListElement from '../elements/itemsListElement';
import TextArea from '../elements/textArea';
import VisualCue from '../elements/visualCue';

export default class CheckoutConsumerPage extends BaseCheckoutPage {
  readonly checkoutButton: Button;
  readonly gotItButton: Button;
  readonly promoCodeButton: Button;
  readonly promoCodeInput: TextArea;
  readonly promoCodeApplyButton: Button;
  readonly collectionTypeButton: Button;
  readonly checkoutDiscount: VisualCue;
  readonly checkoutTotal: VisualCue;
  readonly closeAnimationButton: Button;

  readonly collectionButtonOnAddressModal: Button;
  readonly deliveryButtonOnAddressModal: Button;
  readonly deliveryOptionsMenuOptionButton: Button;
  readonly deliveryBookedButton: Button;
  readonly collectionPointsOptions: DropdownList;
  readonly collectionPointsOnSiteMenuButton: Button;

  readonly receiptAnimationConfirmationButton: Button;

  //Final screen assertion elements:
  readonly droppingLocationTitle: VisualCue;
  readonly addedReceiptsTitle: VisualCue;
  readonly addedPackagesTitle: VisualCue;
  readonly addedPackagesIds: ItemsListElement<VisualCue>;

  // Relevant only on PC
  readonly deliveryTypeModalButton: Button;
  readonly collectionTypeModalButton: Button;
  readonly deliveryOptionsSaveButton: Button;

  constructor(page: Page) {
    super(page);

    this.deliveryOptionsSaveButton = new Button(page.getByTestId('deliveryOptionsSaveButton'));
    this.collectionPointsOptions = new DropdownList(
      page.getByTestId('collectOnSiteSelectInputPressable'),
      page.locator('[data-testid^="collectOnSiteSelectInputDropdown"]'),
    );
    this.checkoutButton = new Button(page.getByTestId('checkoutButton'));
    this.gotItButton = new Button(page.getByTestId('collectionTimeInfoModalCtaButton'));
    this.promoCodeButton = new Button(page.getByTestId('promoCodeCardButton'));
    this.promoCodeInput = new TextArea(page.getByTestId('promoCodeModalInputTextInput'));
    this.promoCodeApplyButton = new Button(page.getByTestId('promoCodeModalPrimaryButton'));
    this.collectionTypeModalButton = new Button(page.getByTestId('deliveryTypeSelectorModalCollectOnSiteButton'));
    this.collectionPointsOnSiteMenuButton = new Button(page.getByTestId('collectOnSiteButtonFormButton'));
    this.deliveryTypeModalButton = new Button(page.getByTestId('deliveryTypeSelectorModalCustomerButton'));
    this.collectionButtonOnAddressModal = new Button(page.getByTestId('deliveryTypeSelectorCollectOnSiteButton'));
    this.deliveryButtonOnAddressModal = new Button(page.getByTestId('deliveryTypeSelectorCustomerButton'));
    this.deliveryOptionsMenuOptionButton = new Button(page.getByTestId('deliveryOptionsCardButton'));
    this.deliveryBookedButton = new Button(page.getByTestId('funModalDeliveryBookedHubOkButton'));

    this.receiptAnimationConfirmationButton = new Button(page.getByTestId('ReceiptsExplanationModalPrimaryButton'));

    this.checkoutDiscount = new VisualCue(page.getByTestId('checkoutDiscount'));
    this.checkoutTotal = new VisualCue(page.getByTestId('checkoutTotal'));
    this.closeAnimationButton = new Button(page.getByTestId('ReceiptsExplanationModalCloseButton'));
    this.droppingLocationTitle = new VisualCue(page.getByTestId('bookingSummaryStoreIndicator'));
    this.addedReceiptsTitle = new VisualCue(page.getByTestId('receiptsCardCount'));
    this.addedPackagesTitle = new VisualCue(page.getByTestId('packagesButtonListSize'));
    this.addedPackagesIds = new ItemsListElement(page.locator('[data-testid^="packagesListItem:"]'), VisualCue);
  }

  public async getDeliveryDetails(): Promise<DeliveryDetailsInConsumerApp> {
    const droppingLocation = await this.droppingLocationTitle.textContent({ shouldClick: false });
    const packageText = await this.addedPackagesTitle.textContent({ shouldClick: false });
    const receiptText = await this.addedReceiptsTitle.textContent({ shouldClick: false });
    const allPackageIds = await this.addedPackagesIds.getAllResults();

    const scannedPackageCodes = await Promise.all(
      allPackageIds.map(async (packageId) => await packageId.textContent({ shouldClick: false })),
    );
    const numberOfPackages = DataManager.DataConverter.convertTextToNumber(packageText);

    // Currently the first number that is shown in the receipts info is the number of items/receipts
    // if it will change it will bring the wrong result, the conversion only takes into considering the first number it gets
    const numberOfReceipts = DataManager.DataConverter.convertTextToNumber(receiptText);

    return {
      scannedPackageCodes,
      numberOfReceipts,
      droppingLocation,
      numberOfPackages,
    };
  }

  public override getInitialUrl() {
    return super.getInitialUrl() + '/new-delivery';
  }

  override async navigate() {
    await super.navigate();
    await this.gotItButton.click();
  }
}

import { Page } from '@playwright/test';
import Button from '../elements/button';
import BaseConsumerPage from './baseConsumerPage';

export default class SettingsPage extends BaseConsumerPage {
  readonly languageSelector: Button;

  constructor(page: Page) {
    super(page);
    this.languageSelector = new Button(page.getByTestId('LanguageSelector'));
  }

  public override getInitialUrl() {
    return super.getInitialUrl() + '/settings';
  }
}

import { Page } from '@playwright/test';
import Button from '../elements/button';
import BaseConsumerPage from './baseConsumerPage';

export default class HomeConsumerPage extends BaseConsumerPage {
  readonly homeSiteGetStartedButton: Button;
  readonly storeGetStartedButton: Button;
  readonly homeSiteFinishDroppingButton: Button;
  readonly finishedDroppingButton: Button;
  readonly okGotItButton: Button;

  constructor(page: Page) {
    super(page);

    this.homeSiteGetStartedButton = new Button(page.getByTestId('HomeSiteGetStartedButton'));
    this.storeGetStartedButton = new Button(page.getByTestId('StoreGetStartedButton'));
    this.homeSiteFinishDroppingButton = new Button(page.getByTestId('HomeSiteFinishDroppingButton'));
    this.finishedDroppingButton = new Button(page.getByTestId('finishedDropping_modal_primary'));
    this.okGotItButton = new Button(page.getByTestId('successModalPrimaryButton'));
  }

  public override getInitialUrl() {
    return super.getInitialUrl() + '/home';
  }
}

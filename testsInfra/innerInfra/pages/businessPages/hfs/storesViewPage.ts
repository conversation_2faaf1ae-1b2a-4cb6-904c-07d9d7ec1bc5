import { Page } from '@playwright/test';
import StoreCardElement from '../../elements/cards/storeCardElement';
import DropdownList from '../../elements/dropDownlist';
import ItemsListElement from '../../elements/itemsListElement';
import TextArea from '../../elements/textArea';
import NavigationBusinessPage from '../navigationBusinessPage';

export default class StoresViewPage extends NavigationBusinessPage {
  readonly siteDropDownFilter: DropdownList;
  readonly storeNameFilter: TextArea;
  readonly vendorDropDownFilter: DropdownList;
  readonly groupDropDownFilter: DropdownList;
  readonly locationDropDownFilter: DropdownList;

  readonly groupResultsByDropDown: DropdownList;
  readonly storeCards: ItemsListElement<StoreCardElement>;

  constructor(page: Page) {
    super(page);

    const commonFilterSelector = 'StoresGlobalView.CustomBox.div.CustomBox.StandardSelect';
    const menuItemSelector = 'StandardSelect.Select.MenuItem';
    const clearButtonSelector = 'StandardSelect.CloseIcon';

    this.siteDropDownFilter = new DropdownList(
      page.getByTestId(commonFilterSelector),
      page.getByTestId(menuItemSelector),
      page.getByTestId(clearButtonSelector),
    );

    this.storeNameFilter = new TextArea(page.getByRole('textbox', { name: 'Search...' }));

    this.vendorDropDownFilter = new DropdownList(
      page.getByTestId(commonFilterSelector + '2'),
      page.getByTestId(menuItemSelector),
      page.getByTestId(clearButtonSelector),
    );

    this.groupDropDownFilter = new DropdownList(
      page.getByTestId(commonFilterSelector + '3'),
      page.getByTestId(menuItemSelector),
      page.getByTestId(clearButtonSelector),
    );

    this.locationDropDownFilter = new DropdownList(
      page.getByTestId(commonFilterSelector + '4'),
      page.getByTestId(menuItemSelector),
      page.getByTestId(clearButtonSelector),
    );

    this.groupResultsByDropDown = new DropdownList(
      page.getByTestId(commonFilterSelector + '5'),
      page.getByTestId(menuItemSelector),
      page.getByTestId(clearButtonSelector),
    );

    this.storeCards = new ItemsListElement(
      page.getByTestId('StoreInfoCard').filter({
        hasNotText: 'Store--------------------------------------------',
      }),
      StoreCardElement,
    );
  }

  public override getInitialUrl() {
    return `${super.getInitialUrl()}global/stores`;
  }
}

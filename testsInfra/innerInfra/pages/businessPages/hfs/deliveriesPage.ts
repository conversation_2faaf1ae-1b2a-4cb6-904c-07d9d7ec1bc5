import { Page } from '@playwright/test';
import TextArea from '../../elements/textArea';
import NavigationBusinessPage from '../navigationBusinessPage';

export default class DeliveriesPage extends NavigationBusinessPage {
  readonly deliveryIdFilter: TextArea;

  constructor(page: Page) {
    super(page);
    this.deliveryIdFilter = new TextArea(page.getByTestId('queryHeader_deliveryId'));
  }

  public override getInitialUrl() {
    return `${super.getInitialUrl()}/deliveries`;
  }
}

import { Page } from '@playwright/test';
import TextArea from '../../elements/textArea';
import NavigationBusinessPage from '../navigationBusinessPage';

export default class UpcomingPickupsPage extends NavigationBusinessPage {
  readonly storeFilter: TextArea;

  constructor(page: Page) {
    super(page);
    this.storeFilter = new TextArea(page.getByTestId('queryHeader_businessName'));
  }

  public override getInitialUrl() {
    return `${super.getInitialUrl()}/upcoming-store-packages`;
  }
}

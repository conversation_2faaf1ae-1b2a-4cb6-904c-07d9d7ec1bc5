import { Page } from '@playwright/test';
import ItemsListElement from '../../elements/itemsListElement';
import VisualCue from '../../elements/visualCue';
import NavigationBusinessPage from '../navigationBusinessPage';

export default class VendorDetailsPage extends NavigationBusinessPage {
  readonly vendorDetailsContent: VisualCue;
  readonly configCards: ItemsListElement<VisualCue>;

  constructor(page: Page) {
    super(page);

    this.vendorDetailsContent = new VisualCue(page.getByTestId('VendorDetailsContent'));
    this.configCards = new ItemsListElement(page.getByTestId('ConfigCard'), VisualCue);
  }

  public override getInitialUrl() {
    return `${super.getInitialUrl()}vendors/`;
  }
}

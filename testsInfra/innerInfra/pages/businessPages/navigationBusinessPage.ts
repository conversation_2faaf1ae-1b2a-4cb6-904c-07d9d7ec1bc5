import { Page } from '@playwright/test';
import Button from '../elements/button';
import DropdownListWithSearch from '../elements/dropDownlistWithSearch';
import VisualCue from '../elements/visualCue';
import BaseBusinessPage from './baseBusinessPage';

export default class NavigationBusinessPage extends BaseBusinessPage {
  readonly switchSiteContextButton: Button;
  readonly siteSelectionDropdownList: DropdownListWithSearch;
  readonly operationsHubSidebarButton: Button;
  readonly salesSummaryCard: VisualCue;
  readonly appHeaderElement: VisualCue;
  readonly intercomButton: Button;
  readonly storesViewButton: Button;
  readonly vendorsViewButton: Button;
  readonly returnsButton: Button;
  readonly pickupsButton: Button;
  readonly deliveriesButton: Button;
  readonly userProfileButton: Button;
  private readonly _logoutButton: Button;

  constructor(page: Page) {
    super(page);

    this.switchSiteContextButton = new Button(page.getByTestId('switchSiteContextButtonText'));
    this.siteSelectionDropdownList = new DropdownListWithSearch(
      this.page.getByTestId('SelectMenuSearchBar.input'),
      this.page.locator("[data-testid='switchSiteContextMenuListContainer'] li"),
    );

    this.operationsHubSidebarButton = new Button(page.getByTestId('sidebar-link:Operations Hub'));
    this.appHeaderElement = new VisualCue(page.getByTestId('AppHeader.StyledAppHeader.Toolbar.CustomBox'));
    this.salesSummaryCard = new VisualCue(page.getByTestId('SalesSummary').getByTestId('EChartCard'));
    this.intercomButton = new Button(page.getByTestId('IconIntercom'));
    this.storesViewButton = new Button(page.getByTestId('sidebar-link:Stores'));
    this.vendorsViewButton = new Button(page.getByTestId('sidebar-link:Vendors'));
    this.returnsButton = new Button(page.getByTestId('sidebar-link:Returns'));
    this.pickupsButton = new Button(page.getByTestId('sidebar-link:Upcoming Pickups'));
    this.deliveriesButton = new Button(page.getByTestId('sidebar-link:Deliveries'));
    this.userProfileButton = new Button(page.getByTestId('UserProfile'));
    this._logoutButton = new Button(page.getByTestId('sidebar-link:Logout'));
  }

  async getLogoutButton() {
    if (await this._logoutButton.isVisible()) {
      return this._logoutButton;
    }

    await this.userProfileButton.click();

    return this._logoutButton;
  }
}

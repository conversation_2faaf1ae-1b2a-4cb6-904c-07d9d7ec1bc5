import { Page } from '@playwright/test';
import DataManager from '../../../dataManager';
import BasePage from '../basePage';

export default class BaseBusinessPage extends BasePage {
  constructor(page: Page, url?: string) {
    super(page, url);
  }

  public override getInitialUrl() {
    return `https://stores.${DataManager.Consts.TESTING_ENV}.drpt.io/business/`;
  }

  protected override getUrlPattern(): RegExp {
    return /drpt.io\/business/;
  }
}

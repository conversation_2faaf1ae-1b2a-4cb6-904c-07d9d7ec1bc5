import { Page } from '@playwright/test';
import DataManager from '../../../dataManager';
import BaseCheckoutPage from '../checkoutPages/baseCheckoutPage';
import Button from '../elements/button';
import PhoneNumberField from '../elements/phoneNumberField';
import TextArea from '../elements/textArea';
import VisualCue from '../elements/visualCue';

export default class CheckoutFulfillmentPage extends BaseCheckoutPage {
  readonly checkoutButton: Button;
  readonly deliveryConfirmationButton: Button;
  readonly phoneInput: PhoneNumberField;
  readonly consumerFirstName: TextArea;
  readonly consumerLastName: TextArea;
  readonly thirdPartyDeliveryOption: Button;
  readonly dropitDeliveryOption: Button;

  readonly orderConfirmationNumber: VisualCue;
  readonly printLabelButton: Button;

  constructor(page: Page) {
    super(page);

    this.checkoutButton = new Button(page.getByTestId('bookingSummaryBookButton'));
    this.deliveryConfirmationButton = new Button(page.getByTestId('deliveryConfirmationButton'));
    this.phoneInput = new PhoneNumberField(
      page,
      page.getByTestId('contactInfoPhoneTextInput'),
      page.getByTestId('contactInfoPhoneCountryPicker'),
      page.getByTestId('contactInfoPhoneSearchDropdown-textInput'),
      page.getByTestId('contactInfoPhoneInputError'),
    );
    this.consumerFirstName = new TextArea(page.getByTestId('contactInfoFirstNameTextInput'));
    this.consumerLastName = new TextArea(page.getByTestId('contactInfoLastNameTextInput'));
    this.thirdPartyDeliveryOption = new Button(page.getByTestId('expressDeliveryOption'));
    this.dropitDeliveryOption = new Button(page.getByTestId('deliveryOptionsQuoteCardButton.drpt-sameday'));
    this.printLabelButton = new Button(page.getByTestId('funModalDeliveryBookedStandalonePrintLabelsButton'));
  }

  public override getInitialUrl() {
    return `https://fulfillment-app.${DataManager.Consts.TESTING_ENV}.drpt.io/new-delivery`;
  }
}

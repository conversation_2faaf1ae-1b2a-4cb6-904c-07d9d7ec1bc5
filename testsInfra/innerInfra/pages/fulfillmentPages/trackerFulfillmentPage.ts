import { Page } from '@playwright/test';
import DataManager from '../../../dataManager';
import DeliveryTrackerStepStatusEnum from '../../../enums/deliveryTrackerStepStatusEnum';
import { TrackerPage } from '../../interfaces/trackerPage';
import { BaseTrackerPage } from '../baseTrackerPage';
import Button from '../elements/button';
import ItemsListElement from '../elements/itemsListElement';
import TextArea from '../elements/textArea';
import BaseFulfillmentPage from './baseFulfillmentPage';

export default class TrackerFulfillmentPage extends BaseFulfillmentPage implements TrackerPage {
  private baseTrackerPage: BaseTrackerPage;

  constructor(page: Page) {
    super(page);
    this.baseTrackerPage = new BaseTrackerPage(page);
  }

  getStepStatus(stepName: string): Promise<DeliveryTrackerStepStatusEnum | null> {
    return this.baseTrackerPage.getStepStatus(stepName);
  }

  get searchInput(): TextArea {
    return this.baseTrackerPage.searchInput;
  }

  get searchResults(): ItemsListElement {
    return this.baseTrackerPage.searchResults;
  }

  get onTheWayStepTitle(): Button {
    return this.baseTrackerPage.onTheWayStepTitle;
  }

  public override getInitialUrl() {
    return `https://fulfillment-app.${DataManager.Consts.TESTING_ENV}.drpt.io/track-orders`;
  }
}

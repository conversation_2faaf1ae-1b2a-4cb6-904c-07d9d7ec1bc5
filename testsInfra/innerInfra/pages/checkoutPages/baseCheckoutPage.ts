import { Page } from '@playwright/test';
import { CheckoutPage } from '../../interfaces/checkoutPage';
import BasePage from '../basePage';
import Button from '../elements/button';
import DropdownListWithSearch from '../elements/dropDownlistWithSearch';
import ScanPackageInput from '../elements/scanPackageInput';
import TextArea from '../elements/textArea';
import VisualCue from '../elements/visualCue';

export default abstract class BaseCheckoutPage extends BasePage implements CheckoutPage {
  readonly receiptsCardButton: Button;
  readonly receiptsManualMoneyTextInput: TextArea;
  readonly receiptsCameraModalCloseButton: Button;
  readonly receiptsCaptureImageButton: Button;
  readonly saveReceiptButton: Button;
  readonly packagesCardButton: Button;
  readonly scanPackagesInputText: TextArea;
  readonly packageScannerManualSaveButton: Button;
  readonly packageDeleteButton: (packageNumber: string) => Button;
  readonly packageScannerModalAddManualButton: Button;
  readonly packageScannerManualScanInputTextInput: TextArea;
  readonly packageScannerManualScanInputSaveButton: Button;
  readonly packageScannerModalSaveButton: Button;
  readonly packageScannerModalDeleteButton: (packageNumber: string) => Button;
  readonly addressMenuOptionButton: Button;
  readonly addressSearchInput: DropdownListWithSearch;
  readonly addressSaveButton: Button;
  readonly hotelRoomNumber: TextArea;
  readonly deliveryOptionsMenuOptionButton: Button;
  readonly deliveryOptionsSaveButton: Button;
  readonly defaultDeliveryPassOption: Button;
  readonly contactInfoNotesTextInput: TextArea;
  readonly confirmCheckoutOkButton: Button;
  readonly failedToDropErrorModal: Button;
  readonly scanPackageInputError: VisualCue;
  readonly packageScannerSaveErrorPopup: VisualCue;

  // Relevant only on mobile
  readonly pageNewPackageScannerModalCloseButton: Button;

  constructor(page: Page) {
    super(page);
    this.receiptsCardButton = new Button(page.getByTestId('receiptsCardButton'));
    this.receiptsManualMoneyTextInput = new TextArea(page.getByTestId('receiptsManualMoneyTextInput'));
    this.receiptsCameraModalCloseButton = new Button(this.page.getByTestId('receiptsCameraModalCloseButton').first());
    this.packagesCardButton = new Button(page.getByTestId('packagesCardButton'));
    this.scanPackagesInputText = new ScanPackageInput({ page });
    this.packageScannerManualSaveButton = new Button(page.getByTestId('packageScannerManualSaveButton'));
    this.packageScannerModalAddManualButton = new Button(page.getByTestId('newPackageScannerModalAddManualButton'));
    this.packageScannerManualScanInputTextInput = new TextArea(page.getByTestId('manualScanModalInputTextInput'));
    this.packageScannerManualScanInputSaveButton = new Button(page.getByTestId('manualScanModalPrimaryButton'));
    this.packageScannerModalSaveButton = new Button(page.getByTestId('newPackageScannerModalSaveButton'));
    this.confirmCheckoutOkButton = new Button(page.getByTestId('funModalConfirmLabelsOkButton'));

    // There are two viable buttons to close the modal, it doesn't matter which one is picked
    this.pageNewPackageScannerModalCloseButton = new Button(
      this.page.getByTestId('newPackageScannerModalCloseButton').first(),
    );

    this.packageDeleteButton = (packageNumber: string) => {
      return new Button(page.getByTestId(`packageScannerManualBagDeleteButton-${packageNumber}`));
    };
    this.packageScannerModalDeleteButton = (packageNumber: string) => {
      return new Button(page.getByTestId(`newPackageScannerModalBagDeleteButton-${packageNumber}`));
    };

    this.addressMenuOptionButton = new Button(page.getByTestId('addressCardButton'));
    this.addressSearchInput = new DropdownListWithSearch(
      page.getByTestId('addressResolverSearchInputText'),
      page.getByTestId('addressResolverDropDownItem'),
    );
    this.hotelRoomNumber = new TextArea(page.getByTestId('contactInfoRoomNumberTextInput'));
    this.addressSaveButton = new Button(page.getByTestId('addressFormSaveButton').locator('visible=true'));
    this.deliveryOptionsMenuOptionButton = new Button(page.getByTestId('deliveryOptionsCardButton'));
    this.deliveryOptionsSaveButton = new Button(page.getByTestId('deliveryOptionsSaveButton'));
    this.defaultDeliveryPassOption = new Button(page.locator('[data-testid*=deliveryOptionsQuoteCardButton]').first());
    this.contactInfoNotesTextInput = new TextArea(page.getByTestId('contactInfoNotesTextInput'));
    this.failedToDropErrorModal = new Button(page.getByTestId('errorModalContainerButton'));
    this.scanPackageInputError = new VisualCue(page.getByTestId('scanPackagesInputError'));
    this.packageScannerSaveErrorPopup = new VisualCue(page.getByTestId('TooltipBody'));
    this.receiptsCaptureImageButton = new Button(page.getByTestId('receiptsCameraModalCaptureButton'));
    this.saveReceiptButton = new Button(page.getByTestId('receiptsCameraModalSaveButton'));
  }

  protected override getUrlPattern(): RegExp {
    return /drpt.io/;
  }
}

import { Page } from '@playwright/test';
import BasePage from './basePage';
import Button from './elements/button';
import VisualCue from './elements/visualCue';

export default class IntercomModal extends BasePage {
  private _sendUsMessage?: Button;
  private _messageTextbox?: VisualCue;
  private _sendMessage?: Button;
  private _operatorMessage?: VisualCue;
  private _recentConversations?: VisualCue;

  constructor(page: Page) {
    super(page);
  }

  private async getFrame() {
    const frameLocator = this.page.locator('iframe[name="intercom-messenger-frame"]').first();

    const frame = await frameLocator.contentFrame();
    if (!frame) {
      throw new Error('❌ Intercom frame not found');
    }
    return frame;
  }

  async init(): Promise<this> {
    const frame = await this.getFrame();

    this._sendUsMessage = new Button(frame.getByRole('button', { name: 'Send us a message' }));
    this._messageTextbox = new VisualCue(frame.getByRole('textbox', { name: 'Message…' }));
    this._sendMessage = new Button(frame.getByRole('button', { name: 'Send a message…' }));
    this._operatorMessage = new VisualCue(frame.getByLabel('Operator says…'));
    this._recentConversations = new VisualCue(frame.getByTestId('recent-conversations'));

    return this;
  }

  private _ensureInitialized<T>(property: T | undefined, propertyName: string): T {
    if (!property) {
      throw new Error(`IntercomModal: ${propertyName} accessed before init() was called`);
    }

    return property;
  }

  get sendUsMessage(): Button {
    return this._ensureInitialized(this._sendUsMessage, 'sendUsMessage');
  }

  get messageTextbox(): VisualCue {
    return this._ensureInitialized(this._messageTextbox, 'messageTextbox');
  }

  get sendMessage(): Button {
    return this._ensureInitialized(this._sendMessage, 'sendMessage');
  }

  get operatorMessage(): VisualCue {
    return this._ensureInitialized(this._operatorMessage, 'operatorMessage');
  }

  get recentConversations(): VisualCue {
    return this._ensureInitialized(this._recentConversations, 'recentConversations');
  }
}

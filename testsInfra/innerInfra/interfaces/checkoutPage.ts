import Button from '../pages/elements/button';
import DropdownListWithSearch from '../pages/elements/dropDownlistWithSearch';
import ScanPackageInput from '../pages/elements/scanPackageInput';
import TextArea from '../pages/elements/textArea';
import VisualCue from '../pages/elements/visualCue';

export interface CheckoutPage {
  // Package related elements
  packagesCardButton: Button;
  scanPackagesInputText: ScanPackageInput;
  packageScannerManualSaveButton: Button;
  packageDeleteButton: (packageNumber: string) => Button;
  pageNewPackageScannerModalCloseButton: Button;

  // Manual package addition elements inside image upload flow
  packageScannerModalAddManualButton: Button;
  packageScannerManualScanInputTextInput: TextArea;
  packageScannerManualScanInputSaveButton: Button;
  packageScannerModalSaveButton: Button;
  packageScannerModalDeleteButton: (packageNumber: string) => Button;

  scanPackageInputError: VisualCue;
  packageScannerSaveErrorPopup: VisualCue;

  // Receipt related elements
  receiptsCardButton: Button;
  receiptsManualMoneyTextInput: TextArea;
  receiptsCameraModalCloseButton: Button;
  receiptsCaptureImageButton: Button;
  saveReceiptButton: Button;

  // Address related elements
  addressMenuOptionButton: Button;
  addressSearchInput: DropdownListWithSearch;
  addressSaveButton: Button;
  contactInfoNotesTextInput: TextArea;
  hotelRoomNumber: TextArea;

  // Delivery options related elements
  deliveryOptionsMenuOptionButton: Button;
  deliveryOptionsSaveButton: Button;
  defaultDeliveryPassOption: Button;
}

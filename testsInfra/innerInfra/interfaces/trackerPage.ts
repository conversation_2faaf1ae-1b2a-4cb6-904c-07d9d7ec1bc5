import DeliveryTrackerStepStatusEnum from '../../enums/deliveryTrackerStepStatusEnum';
import Button from '../pages/elements/button';
import ItemsListElement from '../pages/elements/itemsListElement';
import TextArea from '../pages/elements/textArea';

export interface TrackerPage {
  searchInput: TextArea;
  searchResults: ItemsListElement;
  onTheWayStepTitle: Button;
  getStepStatus: (stepName: string) => Promise<DeliveryTrackerStepStatusEnum | null>;
}

/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-function-type */
import test from '@playwright/test';
import { Logger } from './logger';

export function TestStep(stepName?: string, argsToStringCount = 0) {
  return function decorator(target: Function, context: ClassMethodDecoratorContext) {
    const methodName = context.name as string;

    return async function decoratedMethod(this: any, ...args: any) {
      let testStepName = stepName ?? methodName;
      if (argsToStringCount > 0) {
        testStepName += ` ${args.slice(0, argsToStringCount).join(', ')}`;
      }

      Logger.step(`-----------------------------------------------------------------------`);
      Logger.step(`🧭 Step: ${testStepName}`);
      return test.step(testStepName, async () => await target.call(this, ...args));
    };
  };
}

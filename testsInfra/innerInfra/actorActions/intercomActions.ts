import { Locator } from '@playwright/test';
import { TestStep } from '../decorators';
import { ConsumerContext } from '../interfaces/consumerContext';
import { LocalOperationsUser } from '../interfaces/localOperationsUser';
import { Logger } from '../logger';
import IntercomModal from '../pages/intercomModal';
import BaseAction from './baseAction';

type IntercomActorContext = ConsumerContext | LocalOperationsUser;

export default class IntercomActions extends BaseAction<IntercomActorContext> {
  @TestStep('Start A Conversation With Intercom')
  async startConversationWithIntercom({ shouldNavigateToPage = true }: { shouldNavigateToPage?: boolean } = {}) {
    if (shouldNavigateToPage) {
      await this.actor.goTo.intercomModal();
    }

    const intercomPage = new IntercomModal(this.actor.page);
    await intercomPage.init();

    await intercomPage.sendUsMessage.click();
    await intercomPage.messageTextbox.click();
    await intercomPage.messageTextbox.fill('hello!');
    await intercomPage.sendMessage.click();
  }

  @TestStep('Assert Intercom Conversation')
  async assertIntercomConversation(assertion: (message: Locator) => void) {
    const intercomPage = new IntercomModal(this.actor.page);
    await intercomPage.init();

    // For some reason intercom might close the conversation after we send a message, so we need to reopen it
    if (!(await intercomPage.operatorMessage.waitToBeVisible({ timeout: 10000 }))) {
      Logger.warning('Intercom conversation was closed, reopening the last conversation');
      await intercomPage.recentConversations.click();
    }

    await assertion(intercomPage.operatorMessage.locator);
  }
}

import DropTypesEnum from '../../../enums/dropTypesEnum';
import { TestStep } from '../../decorators';
import { DroppingUser } from '../../interfaces/actorInterfaces/droppingUser';
import { ConsumerContext } from '../../interfaces/consumerContext';
import { Store } from '../../interfaces/store';
import CheckoutConsumerPage from '../../pages/consumerPages/checkoutConsumerPage';
import HomeConsumerPage from '../../pages/consumerPages/homeConsumerPage';
import BaseAction from '../baseAction';

export default class HomeConsumerActions extends BaseAction<ConsumerContext & DroppingUser> {
  constructor(actor: ConsumerContext & DroppingUser) {
    super(actor);
  }

  @TestStep(`Go to home page`)
  public async goToHomePage({
    fromIntercomModal = false,
    shouldNavigateToPage = true,
  }: { fromIntercomModal?: boolean; shouldNavigateToPage?: boolean } = {}) {
    const homePage = new HomeConsumerPage(this.actor.page);
    if (fromIntercomModal) {
      await homePage.refreshPage();
    }

    await this.actor.perform.navigateToPageIfNeeded({
      shouldNavigateToPage,
      page: homePage,
    });

    await homePage.homeSiteGetStartedButton.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: false,
      timeout: 10000,
      errorMessage: 'Failed to load home page, get started button is not visible',
    });
  }

  @TestStep(`Scans store QR Code through app`)
  public async scanStoreQRCodeThroughApp({
    store,
    shouldNavigateToPage = false,
    shouldUseImages = false,
  }: {
    store: Store;
    shouldNavigateToPage?: boolean;
    shouldUseImages?: boolean;
  }) {
    const homePage = new HomeConsumerPage(this.actor.page);

    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: homePage });
    await homePage.homeSiteGetStartedButton.click();

    if (shouldUseImages) {
      return;
    }

    const storeQRCode = store?.qrCode;

    if (!storeQRCode) {
      throw new Error('Store not found on that site, did you choose the wrong site?');
    }

    await this.actor.page.goto(storeQRCode);
  }

  @TestStep('Press get started button')
  public async pressStartDropButton() {
    const homePage = new HomeConsumerPage(this.actor.page);

    await homePage.storeGetStartedButton.click();
    await homePage.storeGetStartedButton.waitToBeInvisible({
      shouldThrowError: true,
      shouldThrowSoftError: false,
      timeout: 5000,
      errorMessage: 'Failed to load drop page, home page is still visible',
    });

    return new CheckoutConsumerPage(homePage.page);
  }

  @TestStep(`Finishing dropping`)
  public async finishDropping({ shouldNavigateToPage = false } = {}) {
    const homePage = new HomeConsumerPage(this.actor.page);

    if (shouldNavigateToPage) {
      await homePage.navigate();
    }

    if (this.actor.dropType === DropTypesEnum.Collection) {
      await homePage.homeSiteFinishDroppingButton.click();
      await homePage.finishedDroppingButton.click();
      await homePage.okGotItButton.click();

      // Every time we finish dropping, we need to pay for the drop pass again on new drops
      this.actor.set.consumerToPayForNextDropPass();
    }
  }
}

import { expect } from '@playwright/test';
import DataManager from '../../../dataManager';
import Package from '../../../entities/package';
import { TestStep } from '../../decorators';
import { BusinessOwnerContext } from '../../interfaces/businessOwnerContext';
import { DroppingUser } from '../../interfaces/droppingUser';
import CheckoutFulfillmentPage from '../../pages/fulfillmentPages/checkoutFulfillmentPage';
import HomeFulfillmentPage from '../../pages/fulfillmentPages/homeFulfillmentPage';
import CheckoutActions from '../checkoutActions';

export default class BusinessOwnerCheckoutActions extends CheckoutActions<BusinessOwnerContext & DroppingUser> {
  constructor(actor: BusinessOwnerContext & DroppingUser) {
    super(actor);
  }

  public override whatIsThePackageInputScanError(
    checkoutPage: CheckoutFulfillmentPage = new CheckoutFulfillmentPage(this.actor.page),
  ) {
    return super.whatIsThePackageInputScanError(checkoutPage);
  }

  @TestStep(`Check out`)
  public async checkout(checkoutFulfillmentPage?: CheckoutFulfillmentPage, failDrop = false) {
    checkoutFulfillmentPage = checkoutFulfillmentPage ?? new CheckoutFulfillmentPage(this.actor.page);

    await checkoutFulfillmentPage.checkoutButton.click();

    if (failDrop) {
      await checkoutFulfillmentPage.waitForLoad();
      await expect(checkoutFulfillmentPage.failedToDropErrorModal.locator).toBeVisible({
        timeout: DataManager.Consts.LONG_ACTION_TIMEOUT,
      });

      return;
    }

    await checkoutFulfillmentPage.printLabelButton.click();
    await checkoutFulfillmentPage.confirmCheckoutOkButton.click();
  }

  protected async goToAddressPage(checkoutPage: CheckoutFulfillmentPage): Promise<void> {
    await checkoutPage.addressMenuOptionButton.click();
  }

  @TestStep(`Add Consumer's address`)
  public async addTheAddressOfConsumer({
    address,
    additionalContactInfo,
    checkoutFulfillmentPage,
    firstName,
    lastName,
    phoneNumber,
    country,
  }: {
    address: string;
    additionalContactInfo?: string;
    checkoutFulfillmentPage?: CheckoutFulfillmentPage;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    country: string;
  }) {
    checkoutFulfillmentPage = checkoutFulfillmentPage ?? new CheckoutFulfillmentPage(this.actor.page);

    await super.addAddress({
      address,
      additionalContactInfo,
      checkoutPage: checkoutFulfillmentPage,
      shouldClickOnSave: false,
    });

    await checkoutFulfillmentPage.consumerFirstName.fill(firstName);
    await checkoutFulfillmentPage.consumerLastName.fill(lastName);
    await checkoutFulfillmentPage.phoneInput.fillWithCountryCode(phoneNumber, country);

    await checkoutFulfillmentPage.addressSaveButton.click({ shouldScrollIntoView: true });
  }

  public async goToDeliveryPage({ shouldNavigateThroughUI = true }: { shouldNavigateThroughUI?: boolean } = {}) {
    await this.actor.perform.navigateToPageIfNeeded({
      shouldNavigateToPage: true,
      page: new CheckoutFulfillmentPage(this.actor.page),
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        const homePage = new HomeFulfillmentPage(this.actor.page);

        await homePage.newDeliveryButton.click();
      },
    });
  }

  @TestStep(`Add Consumer's address`)
  public async addAddress({
    address,
    additionalContactInfo,
    checkoutFulfillmentPage,
    shouldClickOnSave = true,
    shouldThrowError = true,
  }: {
    address: string;
    additionalContactInfo?: string;
    checkoutFulfillmentPage?: CheckoutFulfillmentPage;
    shouldClickOnSave?: boolean;
    shouldThrowError?: boolean;
  }) {
    checkoutFulfillmentPage = checkoutFulfillmentPage ?? new CheckoutFulfillmentPage(this.actor.page);

    return super.addAddress({
      address,
      additionalContactInfo,
      checkoutPage: checkoutFulfillmentPage,
      shouldClickOnSave,
      shouldThrowError,
    });
  }

  @TestStep(`Drops package(s)`)
  public async dropPackages({
    firstName,
    lastName,
    phoneNumber,
    country,
    packages = [],
    money = '50',
    additionalContactInfo = 'Add a nice message to it',
    shouldNavigateThroughUI = true,
    shouldAllPackageAdditionSucceed: shouldPackageAdditionSucceed = true,
  }: {
    firstName: string;
    lastName: string;
    phoneNumber: string;
    country: string;
    packages?: Package[];
    money?: string;
    additionalContactInfo?: string;
    shouldNavigateThroughUI?: boolean;
    shouldAllPackageAdditionSucceed?: boolean;
  }) {
    if (shouldNavigateThroughUI) {
      await this.goToDeliveryPage({ shouldNavigateThroughUI: shouldNavigateThroughUI });
    }

    const checkoutFulfillmentPage = await new CheckoutFulfillmentPage(this.actor.page);

    await this.addTheAddressOfConsumer({
      address: this.actor.address,
      additionalContactInfo,
      checkoutFulfillmentPage,
      firstName,
      lastName,
      phoneNumber,
      country,
    });

    // Once we will want to test third party delivery, we will need more logic here
    await this.selectDropitPass({
      checkoutPage: checkoutFulfillmentPage,
      dropitPass: checkoutFulfillmentPage.dropitDeliveryOption,
    });

    await this.addReceiptsManually({ money, checkoutPage: checkoutFulfillmentPage });

    const addedPackages = await this.addPackages({
      additionalPackages: packages,
      checkoutPage: checkoutFulfillmentPage,
      shouldBarcodeInputErrorAppear: !shouldPackageAdditionSucceed,
    });

    if (!shouldPackageAdditionSucceed) {
      return addedPackages;
    }

    await this.checkout(checkoutFulfillmentPage, !shouldPackageAdditionSucceed);

    return addedPackages;
  }
}

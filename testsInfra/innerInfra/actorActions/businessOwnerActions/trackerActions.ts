import { expect } from '@playwright/test';
import Package from '../../../entities/package';
import DeliveryStatusEnum from '../../../enums/deliveryStatusEnum';
import DeliveryTrackerStepStatusEnum from '../../../enums/deliveryTrackerStepStatusEnum';
import FulfillmentMenuOptionsEnum from '../../../enums/fulfillmentMenuOptionsEnum';
import { TestStep } from '../../decorators';
import { BusinessOwnerContext } from '../../interfaces/actorInterfaces/actorContexts/businessOwnerContext';
import { DroppingUser } from '../../interfaces/actorInterfaces/droppingUser';
import { Logger } from '../../logger';
import TrackerCardElement from '../../pages/elements/cards/fulfillmentCardElement';
import BaseFulfillmentPage from '../../pages/fulfillmentPages/baseFulfillmentPage';
import HomeFulfillmentPage from '../../pages/fulfillmentPages/homeFulfillmentPage';
import TrackerFulfillmentPage from '../../pages/fulfillmentPages/trackerFulfillmentPage';
import BaseAction from '../baseAction';

export default class TrackerActions extends BaseAction<BusinessOwnerContext & DroppingUser> {
  constructor(actor: BusinessOwnerContext & DroppingUser) {
    super(actor);
  }

  @TestStep(`Go to track orders page`)
  public async goToTrackOrdersPage({
    shouldNavigateThroughUI = true,
    shouldNavigateThroughMenu = true,
  }: { shouldNavigateThroughUI?: boolean; shouldNavigateThroughMenu?: boolean } = {}) {
    await this.actor.perform.navigateToPageIfNeeded({
      shouldNavigateToPage: true,
      page: new TrackerFulfillmentPage(this.actor.page),
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        const homePage = new HomeFulfillmentPage(this.actor.page);

        if (shouldNavigateThroughMenu) {
          await homePage.selectMenuOption(FulfillmentMenuOptionsEnum.TrackOrders);
        } else {
          await homePage.trackOrdersNavigationButton.click();
        }
      },
    });

    const trackerPage = new TrackerFulfillmentPage(this.actor.page);
    await trackerPage.searchInput.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: true,
      timeout: 10000,
      errorMessage: 'Failed to load track orders page, search input is not visible',
    });

    await trackerPage.searchInput.fill('This is random text to make sure page was loaded successfully');

    Logger.info(`Track orders page successfully loaded`);
    await trackerPage.searchInput.clear();

    return trackerPage;
  }

  @TestStep(`Get delivery status from deliveries page`)
  public async getDeliveryStatus({
    packagee,
    shouldNavigateToPage = true,
  }: {
    packagee: Package;
    shouldNavigateToPage?: boolean;
  }): Promise<string> {
    if (shouldNavigateToPage) {
      await this.goToTrackOrdersPage({ shouldNavigateThroughUI: false });
    }

    const trackerPage = await this.searchForOrder({ packagee });

    const firstResult = await trackerPage.searchResults.firstResult();
    const status = (firstResult as TrackerCardElement).statusElement;
    const statusText = await status.textContent({ shouldClick: false });

    Logger.debug(`Package ${packagee.packageCode} status: ${statusText}`);

    return statusText ?? '';
  }

  @TestStep(`Search for order in tracker`)
  public async searchForOrder({ packagee }: { packagee: Package }) {
    const trackerPage = new TrackerFulfillmentPage(this.actor.page);
    await trackerPage.searchInput.fill(packagee.packageCode);

    return trackerPage;
  }

  @TestStep(`Assert delivery status`)
  public async assertDeliveryStatus({
    packagee,
    assertion,
    shouldNavigateToPage = true,
    shouldUseClickForNavigation = false,
  }: {
    packagee: Package;
    assertion: {
      expectFunc: (status: string, expectedStatus: DeliveryStatusEnum) => void;
      expectedStatus: DeliveryStatusEnum;
    };
    shouldNavigateToPage?: boolean;
    shouldUseClickForNavigation?: boolean;
  }): Promise<void> {
    if (shouldNavigateToPage) {
      await this.actor.perform.bringPageToFront();

      const fulfillmentPage = new BaseFulfillmentPage(this.actor.page);
      await this.actor.perform.refreshPage(fulfillmentPage);
      await this.goToTrackOrdersPage({ shouldNavigateThroughUI: shouldUseClickForNavigation });
    }

    const trackerPage = new TrackerFulfillmentPage(this.actor.page);

    const status = await this.getDeliveryStatus({ packagee });
    assertion.expectFunc(status, assertion.expectedStatus);

    const firstResult = await trackerPage.searchResults.firstResult();
    await firstResult.click();

    const stepStatus = await trackerPage.getStepStatus(assertion.expectedStatus);
    expect(
      stepStatus,
      `Expected step ${assertion.expectedStatus} to have status ${DeliveryTrackerStepStatusEnum.Finished}, but got ${stepStatus}`,
    ).toBe(DeliveryTrackerStepStatusEnum.Finished);
  }
}

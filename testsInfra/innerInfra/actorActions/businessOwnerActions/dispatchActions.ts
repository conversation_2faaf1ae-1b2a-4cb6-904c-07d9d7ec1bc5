import Package from '../../../entities/package';
import FulfillmentMenuOptionsEnum from '../../../enums/fulfillmentMenuOptionsEnum';
import { TestStep } from '../../decorators';
import { BusinessOwnerContext } from '../../interfaces/businessOwnerContext';
import { DroppingUser } from '../../interfaces/droppingUser';
import { Logger } from '../../logger';
import DispatchFulfillmentPage from '../../pages/fulfillmentPages/dispatchFulfillmentPage';
import HomeFulfillmentPage from '../../pages/fulfillmentPages/homeFulfillmentPage';
import BaseAction from '../baseAction';

export default class DispatchActions extends BaseAction<BusinessOwnerContext & DroppingUser> {
  constructor(actor: BusinessOwnerContext & DroppingUser) {
    super(actor);
  }

  @TestStep(`Go to dispatch page`)
  async goToDispatchPage({ shouldNavigateThroughUI = true }: { shouldNavigateThroughUI?: boolean } = {}) {
    await this.actor.perform.navigateToPageIfNeeded({
      shouldNavigateToPage: true,
      page: new DispatchFulfillmentPage(this.actor.page),
      shouldUseCustomNavigation: shouldNavigateThroughUI,
      customNavigation: async () => {
        const homePage = new HomeFulfillmentPage(this.actor.page);

        await homePage.selectMenuOption(FulfillmentMenuOptionsEnum.Dispatch);
      },
    });
  }

  @TestStep(`Dispatch packages`)
  async dispatchPackages({
    packages,
    shouldNavigateToPage = true,
    dispatcherName = 'Arya',
  }: {
    packages: Package[];
    shouldNavigateToPage?: boolean;
    dispatcherName?: string;
  }) {
    if (shouldNavigateToPage) {
      await this.goToDispatchPage();
    }

    const dispatchPage = new DispatchFulfillmentPage(this.actor.page);
    await this.searchAndSelectDelivery(dispatchPage, packages[0]);
    await this.fillDispatchDetails(dispatchPage, packages, dispatcherName);
    await this.submitDispatch(dispatchPage);
  }

  @TestStep(`Search by package code and select first delivery`, 1)
  async searchAndSelectDelivery(dispatchPage: DispatchFulfillmentPage, packagee: Package) {
    await dispatchPage.dispatchSearchInput.fill(packagee.packageCode);
    await dispatchPage.dispatchSearchInput.press('Enter');
    await dispatchPage.dispatchListDeliveryResults.waitUntilResultsCount({ expectedCount: 1, exactMatch: true });

    let firstResult = await dispatchPage.dispatchListDeliveryResults.firstResult();
    await firstResult.click();

    if (
      !(await dispatchPage.dispatchedPackages.waitUntilResultsCount({
        expectedCount: 1,
        exactMatch: true,
        shouldThrowError: false,
      }))
    ) {
      Logger.info(
        'Failed to load requested delivery, this could be the automation clicking on the wrong delivery\nTrying again',
      );

      firstResult = await dispatchPage.dispatchListDeliveryResults.firstResult();
      await firstResult.click();
      await dispatchPage.dispatchedPackages.waitUntilResultsCount({ expectedCount: 1, exactMatch: true });
    }
  }

  @TestStep(`Fill dispatch details`)
  async fillDispatchDetails(dispatchPage: DispatchFulfillmentPage, packages: Package[], dispatcherName: string) {
    for (const packagee of packages) {
      await dispatchPage.scanPackagesInput.fill(packagee.packageCode);
    }

    await dispatchPage.dispatchNameInput.fill(dispatcherName);
  }

  @TestStep(`Submit delivery to dispatch`)
  async submitDispatch(dispatchPage: DispatchFulfillmentPage) {
    await dispatchPage.dispatchSubmitButton.click();
    await dispatchPage.dispatchOkButton.click();
  }
}

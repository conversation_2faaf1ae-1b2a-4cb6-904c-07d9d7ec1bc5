import { GlobalAdminUser } from '../../interfaces/globalAdminUser';
import StoresViewPage from '../../pages/businessPages/hfs/storesViewPage';
import StoresActions from './storesActions';

export default class StoresActionsGlobalAdmin extends StoresActions<GlobalAdminUser> {
  constructor(actor: GlobalAdminUser) {
    super(actor);
  }

  public async assertStoresViewLoaded() {
    const storesViewPage = new StoresViewPage(this.actor.page);

    await storesViewPage.siteDropDownFilter.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load stores view page, store name filter is not visible',
    });

    return storesViewPage;
  }
}

import { GlobalAdminUserContext } from '../../interfaces/actorInterfaces/actorContexts/globalAdminUserContext';
import { Logger } from '../../logger';
import StoresViewPage from '../../pages/businessPages/hfs/storesViewPage';
import StoresActions from './storesActions';

export default class StoresActionsGlobalAdmin extends StoresActions<GlobalAdminUserContext> {
  constructor(actor: GlobalAdminUserContext) {
    super(actor);
  }

  public async filter({
    siteName,
    VendorName,
    GroupName,
    LocationType,
    GroupResultsBy,
    shouldNavigateToPage = false,
    expectCardsCountToChange = true,
  }: {
    siteName?: string;
    VendorName?: string;
    GroupName?: string;
    LocationType?: string;
    GroupResultsBy?: string;
    shouldNavigateToPage?: boolean;
    expectCardsCountToChange?: boolean;
  }) {
    if (!siteName && !VendorName && !GroupName && !LocationType && !GroupResultsBy) {
      throw new Error('No filter provided, please provide at least one filter when calling the filter function');
    }

    const storesViewPage = new StoresViewPage(this.actor.page);

    await this.actor.perform.navigateToPageIfNeeded({
      shouldNavigateToPage,
      page: storesViewPage,
    });

    await storesViewPage.storeCards.waitUntilResultsCount({
      expectedCount: 1,
      exactMatch: false,
      shouldThrowError: true,
    });

    const cardsCountBeforeFilter = (await storesViewPage.storeCards.getAllResults()).length;
    Logger.info(`Cards count before filter: ${cardsCountBeforeFilter}`);

    if (siteName) {
      await storesViewPage.siteDropDownFilter.selectOption({ optionToSelect: siteName });
    }

    if (VendorName) {
      await storesViewPage.vendorDropDownFilter.selectOption({ optionToSelect: VendorName });
    }

    if (GroupName) {
      await storesViewPage.groupDropDownFilter.selectOption({ optionToSelect: GroupName });
    }

    if (LocationType) {
      await storesViewPage.locationDropDownFilter.selectOption({ optionToSelect: LocationType });
    }

    if (GroupResultsBy) {
      await storesViewPage.groupResultsByDropDown.selectOption({ optionToSelect: GroupResultsBy });
    }

    if (expectCardsCountToChange) {
      await storesViewPage.storeCards.waitForResultsToBeDifferentThan({ previousResultsCount: cardsCountBeforeFilter });
    }

    Logger.info(`Cards count after filter: ${(await storesViewPage.storeCards.getAllResults()).length}`);
  }

  public async assertStoresViewLoaded() {
    const storesViewPage = new StoresViewPage(this.actor.page);

    await storesViewPage.siteDropDownFilter.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load stores view page, store name filter is not visible',
    });

    return storesViewPage;
  }
}

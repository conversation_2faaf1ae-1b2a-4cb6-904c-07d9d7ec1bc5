import { TestStep } from '../../decorators';
import { LocalOperationsUserContext } from '../../interfaces/actorInterfaces/actorContexts/localOperationsUserContext';
import StoresViewPage from '../../pages/businessPages/hfs/storesViewPage';
import StoresActions from './storesActions';

export default class StoresActionsLocalOperator extends StoresActions<LocalOperationsUserContext> {
  constructor(actor: LocalOperationsUserContext) {
    super(actor);
  }

  public async assertStoresViewLoaded() {
    const storesViewPage = new StoresViewPage(this.actor.page);
    await storesViewPage.storeNameFilter.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load stores view page, store name filter is not visible',
    });

    return storesViewPage;
  }

  @TestStep('Filter stores by name')
  public async filterByName({ text, shouldNavigateToPage = true }: { text: string; shouldNavigateToPage?: boolean }) {
    let storesViewPage: StoresViewPage;
    if (!shouldNavigateToPage) {
      storesViewPage = new StoresViewPage(this.actor.page);
    } else {
      storesViewPage = await this.goToStoresViewPage();
    }

    await storesViewPage.storeNameFilter.fill(text);
  }
}

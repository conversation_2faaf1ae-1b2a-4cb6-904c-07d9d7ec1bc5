import { expect } from '@playwright/test';
import Package from '../../../entities/package';
import Site from '../../../entities/site';
import HandOverButtonTypesEnum from '../../../enums/handOverButtonTypeEnum';
import { TestStep } from '../../decorators';
import { Auth0LoginUser } from '../../interfaces/auth0Login';
import { LocalOperationsUser } from '../../interfaces/localOperationsUser';
import { Logger } from '../../logger';
import OperationHubPage from '../../pages/businessPages/hfs/operationsHub';
import Button from '../../pages/elements/button';
import BaseAction from '../baseAction';

export default class OperationHubActions extends BaseAction<LocalOperationsUser & Auth0LoginUser> {
  constructor(actor: LocalOperationsUser & Auth0LoginUser) {
    super(actor);
  }

  @TestStep(`Receive packages`)
  public async receivePackages({
    site,
    packages,
    shouldNavigateToPage = false,
    shouldSearchPackages = true,
  }: {
    site: Site;
    packages: Package[];
    shouldNavigateToPage?: boolean;
    shouldSearchPackages?: boolean;
  }) {
    const operationHubPage = new OperationHubPage(this.actor.page, site);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: operationHubPage });

    if (shouldSearchPackages) {
      await this.searchPackagesReceiveFlow({ packages, operationHubPage });
    } else {
      await this.menuScanPackagesReceiveFlow({ packages, operationHubPage });
    }
  }

  @TestStep(`Hand over delivery`)
  public async handoverDelivery({
    site,
    packages,
    shouldNavigateToPage = false,
    shouldSearchPackages = true,
  }: {
    site: Site;
    packages: Package[];
    shouldNavigateToPage?: boolean;
    shouldSearchPackages?: boolean;
  }) {
    if (!packages?.length) {
      throw new Error('Cannot handover delivery without packages');
    }

    const operationHubPage = new OperationHubPage(this.actor.page, site);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: operationHubPage });

    await this.goToNeededScanPage('Handover Delivery', {
      operationHubPage,
      packageToSearch: packages[0],
      actionButton: await operationHubPage.getHandOverButton(HandOverButtonTypesEnum.InitialHandOver, false),
      menuButton: operationHubPage.handoverPackagesMenuButton,
      shouldSearchPackage: shouldSearchPackages,
    });

    await this.scanPackagesAfterSearch({ packages, operationHubPage, pageName: 'Handover Delivery' });

    await (await operationHubPage.getHandOverButton(HandOverButtonTypesEnum.AfterScan)).click();
    await (await operationHubPage.getHandOverButton(HandOverButtonTypesEnum.FinalHandOver)).click();
  }

  @TestStep(`Search for package`, 1)
  public async searchPackage(packagee: Package, options: { operationHubPage?: OperationHubPage; site?: Site }) {
    if (!packagee) {
      throw new Error('Package is undefined');
    }

    if (!options.operationHubPage) {
      if (!options.site) {
        throw new Error('Site and page cannot be both undefined');
      }

      options.operationHubPage = new OperationHubPage(this.actor.page, options.site);
      await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage: true, page: options.operationHubPage });
    }

    await options.operationHubPage.searchPackageInput.fill(packagee.packageCode);

    return options.operationHubPage;
  }

  @TestStep(`Prepare Delivery for dispatch`)
  public async prepareDeliveryForDispatch({
    site,
    packages,
    shouldNavigateToPage = false,
    shouldSearchPackages = true,
  }: {
    site: Site;
    packages: Package[];
    shouldNavigateToPage?: boolean;
    shouldSearchPackages?: boolean;
  }) {
    const operationHubPage = new OperationHubPage(this.actor.page, site);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: operationHubPage });

    await this.goToNeededScanPage('Prepare Delivery for Dispatch', {
      operationHubPage,
      packageToSearch: packages[0],
      actionButton: operationHubPage.prepareForDispatchButton,
      menuButton: operationHubPage.prepareForDispatchMenuButton,
      shouldSearchPackage: shouldSearchPackages,
    });

    await this.scanPackagesAfterSearch({ packages, operationHubPage, pageName: 'Prepare Delivery for Dispatch' });

    await operationHubPage.updateFooterSubmitButton.click();
    await operationHubPage.doneButton.click();
  }

  @TestStep(`Dispatch delivery`)
  public async dispatchDelivery({
    site,
    packages,
    shouldNavigateToPage = false,
    shouldSearchPackages = true,
  }: {
    site: Site;
    packages: Package[];
    shouldNavigateToPage?: boolean;
    shouldSearchPackages?: boolean;
  }) {
    const operationHubPage = new OperationHubPage(this.actor.page, site);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: operationHubPage });
    await this.goToNeededScanPage('Dispatch Delivery', {
      operationHubPage,
      packageToSearch: packages[0],
      actionButton: operationHubPage.dispatchButton,
      menuButton: operationHubPage.dispatchPackagesMenuButton,
      shouldSearchPackage: shouldSearchPackages,
    });

    await this.scanPackagesAfterSearch({ packages, operationHubPage, pageName: 'Dispatch Delivery' });

    await operationHubPage.updateFooterSubmitButton.click();
    await operationHubPage.finalDispatchButton.click();
  }

  @TestStep(`Login and Receive packages`)
  public async loginAndReceivePackages({
    site,
    packages,
    shouldSearchPackages = true,
  }: {
    site: Site;
    packages: Package[];
    shouldSearchPackages?: boolean;
  }) {
    await this.actor.perform.login();
    await this.actor.perform.goToOperationsHubForTheSite({ site, afterLogin: true });
    await this.receivePackages({ site, packages, shouldSearchPackages });
  }

  @TestStep(`Prepare and dispatch delivery`)
  public async prepareAndDispatchDelivery({
    site,
    packages,
    isLoggedIn = true,
    shouldSearchPackages = true,
  }: {
    site: Site;
    packages: Package[];
    isLoggedIn?: boolean;
    shouldSearchPackages?: boolean;
  }) {
    if (!isLoggedIn) {
      await this.actor.perform.login();
      await this.actor.perform.goToOperationsHubForTheSite({ site, afterLogin: true });
    }

    await this.prepareDeliveryForDispatch({ site, packages, shouldSearchPackages });
    await this.dispatchDelivery({ site, packages, shouldSearchPackages });
  }

  @TestStep(`Scan packages`)
  private async scanPackagesAfterSearch({
    packages,
    operationHubPage,
    pageName,
    shouldExpectPackageChecker = true,
  }: {
    operationHubPage: OperationHubPage;
    packages: Package[];
    pageName: string;
    shouldExpectPackageChecker?: boolean;
  }) {
    await operationHubPage.manualScanPackageCodeInput.waitToBeVisible({
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: `${pageName} Page failed to load, manual scan package code input is not visible`,
      timeout: 5000,
    });

    for (const [i, packagee] of packages.entries()) {
      await operationHubPage.manualScanPackageCodeInput.fill(packagee.packageCode);
      await this.waitForPackagesToAppearAsScanned({
        operationHubPage,
        expectedPackagesCount: i + 1,
        shouldExpectPackageChecker,
      });
    }
  }

  @TestStep(`Wait For Scanned Package to appear as scanned`)
  private async waitForPackagesToAppearAsScanned({
    operationHubPage,
    expectedPackagesCount,
    shouldExpectPackageChecker = true,
  }: {
    operationHubPage: OperationHubPage;
    expectedPackagesCount: number;
    shouldExpectPackageChecker?: boolean;
  }) {
    if (shouldExpectPackageChecker) {
      await expect(
        operationHubPage.scannedPackagesCheckers.locator,
        'Expected number of scanned packages checkers to be equal to the number of packages scanned',
      ).toHaveCount(expectedPackagesCount);
      const checkers = await operationHubPage.scannedPackagesCheckers.locator.all();
      await Promise.all(checkers.map((checker) => expect(checker).toBeVisible()));
    } else {
      await expect(
        operationHubPage.packageType.locator,
        'Expected number of package types to be equal to the number of packages scanned',
      ).toHaveCount(expectedPackagesCount);

      const checkers = await operationHubPage.packageType.locator.all();
      await Promise.all(checkers.map((packageType) => expect(packageType).toBeVisible()));
    }
  }

  @TestStep(`Receiving packages through menu`)
  private async menuScanPackagesReceiveFlow({
    packages,
    operationHubPage,
  }: {
    packages: Package[];
    operationHubPage: OperationHubPage;
  }) {
    await operationHubPage.receivePackagesMenuButton.click();

    await this.scanPackagesAfterSearch({
      packages,
      operationHubPage,
      pageName: 'Receive Packages',
      shouldExpectPackageChecker: false,
    });

    await operationHubPage.barcodePrintButton.click();
    await operationHubPage.okButton.click();
  }

  @TestStep(`Receiving packages through search`)
  private async searchPackagesReceiveFlow({
    packages,
    operationHubPage,
  }: {
    packages: Package[];
    operationHubPage: OperationHubPage;
  }) {
    for (const packagee of packages) {
      await this.searchPackage(packagee, { operationHubPage });
      await operationHubPage.receivePackagesButton.click();

      await operationHubPage.manualScanPackageCodeInput.waitToBeVisible({
        shouldThrowError: true,
        shouldThrowSoftError: true,
        errorMessage: 'Receive Packages Page failed to load, manual scan package code input is not visible',
        timeout: 5000,
      });

      await operationHubPage.manualScanPackageCodeInput.fill(packagee.packageCode);
      await operationHubPage.barcodePrintButton.click();
      await operationHubPage.okButton.click();
    }
  }

  @TestStep(`Navigate to target scan page: `, 1)
  private async goToNeededScanPage(
    pageName: string,
    {
      packageToSearch,
      operationHubPage,
      actionButton = undefined,
      menuButton = undefined,
      shouldSearchPackage = true,
    }: {
      operationHubPage: OperationHubPage;
      packageToSearch: Package;
      shouldSearchPackage?: boolean;
      actionButton?: Button;
      menuButton?: Button;
    },
  ) {
    if (shouldSearchPackage) {
      if (!actionButton) {
        throw new Error('Action button is undefined');
      }

      await this.searchPackage(packageToSearch, { operationHubPage });
      await actionButton.click();
    } else {
      if (!menuButton) {
        throw new Error('Menu button is undefined');
      }

      await menuButton.click();
      await operationHubPage.manualScanPackageCodeInput.fill(packageToSearch.packageCode);
      await operationHubPage.cancelScanButton.waitToBeVisible();
      await operationHubPage.manualScanPackageCodeInput.fill(packageToSearch.packageCode);
    }

    Logger.debug(`Finished navigating to ${pageName} page`, pageName);
  }
}

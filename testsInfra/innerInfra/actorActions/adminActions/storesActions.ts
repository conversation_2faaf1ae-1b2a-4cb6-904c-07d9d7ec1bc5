import { TestStep } from '../../decorators';
import { GlobalAdminUserContext } from '../../interfaces/actorInterfaces/actorContexts/globalAdminUserContext';
import { LocalOperationsUserContext } from '../../interfaces/actorInterfaces/actorContexts/localOperationsUserContext';
import StoreDetailsPage from '../../pages/businessPages/hfs/storeDetailsPage';
import StoresViewPage from '../../pages/businessPages/hfs/storesViewPage';
import NavigationBusinessPage from '../../pages/businessPages/navigationBusinessPage';
import BaseAction from '../baseAction';

type AllowedActors = LocalOperationsUserContext | GlobalAdminUserContext;

export default abstract class StoresActions<T extends AllowedActors = AllowedActors> extends BaseAction<T> {
  constructor(actor: T) {
    super(actor);
  }

  public abstract assertStoresViewLoaded(): Promise<StoresViewPage>;

  @TestStep('Go to stores view page')
  public async goToStoresViewPage({
    shouldNavigateToPage = false,
    viewRandomStoreDetails = false,
  }: { shouldNavigateToPage?: boolean; viewRandomStoreDetails?: boolean } = {}) {
    const navigationBusinessPage = new NavigationBusinessPage(this.actor.page);
    await this.actor.perform.navigateToPageIfNeeded({ shouldNavigateToPage, page: navigationBusinessPage });
    await navigationBusinessPage.storesViewButton.click();

    const storesViewPage = await this.assertStoresViewLoaded();

    if (viewRandomStoreDetails) {
      await this.viewStoreDetails({ storesViewPage });
    }

    return storesViewPage;
  }

  @TestStep('View First Store Details')
  public async viewStoreDetails({
    storesViewPage = new StoresViewPage(this.actor.page),
  }: { storesViewPage?: StoresViewPage } = {}) {
    const firstStoreButton = await storesViewPage.storeCards.firstResult({ timeout: 3000 });
    await firstStoreButton.click();

    const storeDetailsPage = new StoreDetailsPage(this.actor.page);
    await storeDetailsPage.infoButton.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: 'Failed to load store details page, info button is not visible',
    });

    storeDetailsPage.fulfillmentAppButton.click();
    await storeDetailsPage.passwordFulfillmentAppTab.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: `Failed to load store's fulfillment app details tab, password field is not visible`,
    });

    // Sometimes the button didn't fully load yet, so we need to reclick it
    storeDetailsPage.dropPointsButton.click();
    await storeDetailsPage.storeQRCodeDropPointTab.waitUntilResultsCount({
      expectedCount: 1,
      exactMatch: false,
      timeout: 3000,
    });

    storeDetailsPage.dropPointsButton.click();
    const firstQRCode = await storeDetailsPage.storeQRCodeDropPointTab.firstResult();
    await firstQRCode.waitToBeVisible({
      timeout: 5000,
      shouldThrowError: true,
      shouldThrowSoftError: true,
      errorMessage: `Failed to load store's drop points tab, store QR code is not visible`,
    });
  }

  @TestStep('Assert name of every store card')
  public async assertNameOfEveryStoreCard({
    shouldNavigateToPage = false,
    expectedMinimumResultCount,
    assertion,
  }: {
    shouldNavigateToPage?: boolean;
    expectedMinimumResultCount: number;
    assertion: (text: string) => void;
  }) {
    if (shouldNavigateToPage) {
      await this.goToStoresViewPage();
    }

    const storesViewPage = new StoresViewPage(this.actor.page);

    await storesViewPage.storeCards.waitUntilResultsCount({
      expectedCount: expectedMinimumResultCount,
      exactMatch: false,
      shouldThrowError: true,
    });

    const storeCards = await storesViewPage.storeCards.getAllResults();

    for (const card of storeCards) {
      const cardText = await card.textContent({ shouldClick: false });

      assertion(cardText);
    }
  }
}

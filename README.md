# Arya Stark - QA Automation with <PERSON><PERSON>

## Overview

"Arya Stark" is a repository dedicated to automating QA tests using Playwright, a powerful Node.js library for browser automation. This repo aims to streamline and speed up quality assurance processes by using <PERSON><PERSON> to run end-to-end tests on web applications.

## Visual Studio Code Onboarding Guide

- Download VSCode:
  https://code.visualstudio.com/download

- Download the Playwright VSCODE Extension:
  https://marketplace.visualstudio.com/items?itemName=ms-playwright.playwright

- restart VSCode

- run 'npm install' in the project's terminal

- run 'npx playwright install'

- decrypt the env files using
  'npm run decrypt' in the terminal

  - note that 'default/local' is set to qa
  - To switch to another environment go to envFiles/common.env and change the TESTING_ENV variable to the desired environment

- Check this guide for information on how to run the tests
  https://playwright.dev/docs/getting-started-vscode

#### And you are ready to go!

if for some reason the playwright extension doesn't work, try the next things:

1. run npm run clear-cache
2. restart the application

### To view the playwright report downloaded from the pipeline:

- Make sure you have playwright downloaded on your project
- Make sure the downloaded report is in the downloads folder
- run 'npm run report:pipeline' in the terminal
- after you finish viewing the report run 'npm run report:pipeline:delete' in the terminal

- If the name of the downloaded report is NOT 'playwright-report' run 'REPORT_NAME=myCustomReport npm run report:pipeline' in the terminal

###### Note: that command only works in Mac OS

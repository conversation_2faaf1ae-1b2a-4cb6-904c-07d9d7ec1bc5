# Arya Stark | [QA Automation Documentation](https://www.notion.so/dropitshopping/Master-Page-1b265dea3e8d80faa9c0f6c736b9d4a8)

## Overview

"Arya Stark" is a repository dedicated to automating QA tests using Playwright, a powerful Node.js library for browser automation. This repo aims to streamline and speed up quality assurance processes by using <PERSON><PERSON> to run end-to-end tests on web applications.

## Visual Studio Code Onboarding Guide

- Clone the project inside the backend folder(this is to allow for sops encryption, ask devops more info)

- [Download VSCode](https://code.visualstudio.com/download)

- [Download the Playwright VSCODE Extension](https://marketplace.visualstudio.com/items?itemName=ms-playwright.playwright)

- restart VSCode

- Install dependencies:

  ```bash
  npm install
  ```

- Install Playwright browsers:

  ```bash
  npx playwright install
  ```

- Decrypt the environment files:

  ```bash
  npm run env:decrypt
  ```

  - note that 'default/local' is set to qa
  - To switch to another environment go to envFiles/common.env and change the TESTING_ENV variable to the desired environment

- Check this [<PERSON><PERSON>'s Offical Guide](https://playwright.dev/docs/getting-started-vscode) for information on how to run the tests

- **IMPORTANT**:
  - DO NOT give Camera premissions to the IDE otherwise most tests will fail(they don't expect camera premissions)
  - YOU NEED to use VPN to bypass cloudflare and run the the tests on our enviorment, talk with devops for more details

#### And you are ready to go!

if for some reason the playwright extension doesn't work, try the next things:

1. Clear the cache:
   ```bash
   npm run clear-cache
   ```
2. restart the application

### To view the playwright report downloaded from the pipeline:

#### Note: that these command only works in Mac OS

- Make sure you have playwright downloaded on your project
- Make sure the downloaded report is in the downloads folder
- View the pipeline report:
  ```bash
  npm run report:pipeline
  ```
- After you finish viewing the report, clean up:

  ```bash
  npm run report:pipeline:delete
  ```

- If the name of the downloaded report is NOT 'playwright-report', use a custom name:
  ```bash
  REPORT_NAME=myCustomReport npm run report:pipeline
  ```

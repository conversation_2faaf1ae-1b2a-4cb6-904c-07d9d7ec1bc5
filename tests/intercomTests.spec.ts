import { expect } from '@playwright/test';
import { ConsumerUser } from '../testsInfra/actors/consumer';
import GlobalAdminUser from '../testsInfra/actors/globalAdmin';
import DataManager from '../testsInfra/dataManager';
import CreditCard from '../testsInfra/entities/creditCard';
import { test } from './baseTest';

test.describe('Check Intercom is displayed and talkable after login', () => {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: `In Consumer App`,
      ticketNumber: 19,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
      ],
    },
    async ({ mobilePage }) => {
      const site = DataManager.Consts.Sites.CollectionOnlySite;
      const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
        creditCard: new CreditCard(),
      });

      await consumer.perform.startConversationWithIntercom();
      await consumer.assert.intercomConversation((message) => expect(message).toContainText(consumer.email));
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: `In Admin Business App`,
      ticketNumber: 19,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
      ],
    },
    async ({ midPcPage }) => {
      const admin = new GlobalAdminUser(midPcPage);

      await admin.perform.startConversationWithIntercom();
      await admin.assert.intercomConversation((message) => expect(message).toContainText(admin.email));
    },
  );
});

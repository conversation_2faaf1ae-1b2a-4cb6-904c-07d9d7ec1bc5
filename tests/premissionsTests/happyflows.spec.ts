import { expect } from '@playwright/test';
import { ConsumerUser } from '../../testsInfra/actors/consumer';
import LocationOperatorUser from '../../testsInfra/actors/locationOperatorUser';
import DataManager from '../../testsInfra/dataManager';
import DeliveryStatusEnum from '../../testsInfra/enums/deliveryStatusEnum';
import DropTypesEnum from '../../testsInfra/enums/dropTypesEnum';
import { test } from '../baseTest';

const numsOfPackagesToGenerate: number[] = [1, 3];
const shouldSearchPackages = [true, false];

const xPackagesInASingleDeliveryText = (numOfPackagesInDelivery: number, searchPackages: boolean) =>
  `${searchPackages ? 'Admin searches package flow' : 'Admin scans package flow'} ${numOfPackagesInDelivery} packages in a single delivery`;

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: 'Location Operator Admin Will only see things related to the operation hub and their site',
    ticketNumber: 66,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
    ],
  },
  async ({ smallPcPage }) => {
    const admin = new LocationOperatorUser(smallPcPage);

    await admin.perform.login();
    await admin.assert.menuOptions({
      assertion: (siteName) =>
        expect(siteName, { message: 'Asserting site name' }).toContain(
          DataManager.Consts.Sites.LocationOperatorUserSite.name,
        ),
    });

    await admin.goTo.operationsHubPage({ site: DataManager.Consts.Sites.LocationOperatorUserSite, afterLogin: true });
    await admin.goTo.pickupsPage();
    await admin.goTo.deliveriesPage();
    await admin.goTo.storesViewPage();
  },
);

test.describe(`Location Operator User Operation Hub Actions in a Collection and Delivery site`, () => {
  for (const shouldSearchPackage of shouldSearchPackages) {
    for (const numOfPackagesToGenerate of numsOfPackagesToGenerate) {
      test(
        DataManager.DataConverter.convertToTestTitleName({
          titleText: `Two Drops in a single delivery at ${xPackagesInASingleDeliveryText(numOfPackagesToGenerate, shouldSearchPackage)}`,
          ticketNumber: 66,
        }),
        {
          tag: [
            DataManager.Consts.Tags.QA_TAG,
            DataManager.Consts.Tags.DEV_TAG,
            DataManager.Consts.Tags.SANITY_TAG,
            DataManager.Consts.Tags.CONSUMER_APP_TAG,
            DataManager.Consts.Tags.BUSINESS_APP_TAG,
            DataManager.Consts.Tags.CONSUMER_DROP_TAG,
          ],
        },
        async ({ smallPcPage, mobilePage }) => {
          const site = DataManager.Consts.Sites.LocationOperatorUserSite;
          const admin = new LocationOperatorUser(smallPcPage);
          const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
            dropType: DropTypesEnum.Collection,
            address: '123 Main St',
          });

          await consumer.perform.existingConsumerDroppingAPackage({
            shouldFinishDropping: false,
            store: consumer.site.stores[0],
            packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
          });

          await consumer.perform.existingConsumerDroppingAPackage({
            shouldLogin: false,
            store: consumer.site.stores[1],
            packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
          });

          await admin.perform.loginAndReceivePackages({
            site: consumer.site,
            packages: consumer.droppedPackages,
            shouldSearchPackages: shouldSearchPackage,
          });

          await admin.perform.prepareAndDispatchDelivery({
            site: consumer.site,
            packages: consumer.droppedPackages,
            shouldSearchPackages: shouldSearchPackage,
          });

          await admin.perform.deliverPackages({
            site: consumer.site,
            packagee: consumer.droppedPackages[0],
            shouldGoToOperationHubAfterAction: false,
          });

          await admin.assert.deliveryStatus({
            packagee: consumer.droppedPackages[0],
            site: consumer.site,
            assertion: (status) =>
              expect(status, { message: 'Asserting drop status' }).toBe(DeliveryStatusEnum.Delivered),
            shouldNavigateToPage: false,
          });
        },
      );

      test(
        DataManager.DataConverter.convertToTestTitleName({
          titleText: `Collection with ${xPackagesInASingleDeliveryText(numOfPackagesToGenerate, shouldSearchPackage)}`,
          ticketNumber: 66,
        }),
        {
          tag: [
            DataManager.Consts.Tags.QA_TAG,
            DataManager.Consts.Tags.DEV_TAG,
            DataManager.Consts.Tags.SANITY_TAG,
            DataManager.Consts.Tags.CONSUMER_APP_TAG,
            DataManager.Consts.Tags.BUSINESS_APP_TAG,
            DataManager.Consts.Tags.CONSUMER_DROP_TAG,
          ],
        },
        async ({ smallPcPage, mobilePage }) => {
          const site = DataManager.Consts.Sites.LocationOperatorUserSite;
          const admin = new LocationOperatorUser(smallPcPage);
          const consumer = new ConsumerUser(mobilePage, 'Itzik Beja', DataManager.Consts.PHONE_NUMBER2, site, {
            dropType: DropTypesEnum.Collection,
            address: '123 Main St',
          });

          await consumer.perform.existingConsumerDroppingAPackage({
            packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
          });

          await admin.perform.loginAndReceivePackages({
            site: consumer.site,
            packages: consumer.droppedPackages,
            shouldSearchPackages: shouldSearchPackage,
          });

          await admin.perform.prepareAndDispatchDelivery({
            site: consumer.site,
            packages: consumer.droppedPackages,
            shouldSearchPackages: shouldSearchPackage,
          });

          await admin.perform.deliverPackages({
            site: consumer.site,
            packagee: consumer.droppedPackages[0],
            shouldGoToOperationHubAfterAction: false,
          });

          await admin.assert.deliveryStatus({
            packagee: consumer.droppedPackages[0],
            site: consumer.site,
            assertion: (status) =>
              expect(status, { message: 'Asserting drop status' }).toBe(DeliveryStatusEnum.Delivered),
            shouldNavigateToPage: false,
          });
        },
      );

      test(
        DataManager.DataConverter.convertToTestTitleName({
          titleText: `Delivery with ${xPackagesInASingleDeliveryText(numOfPackagesToGenerate, shouldSearchPackage)}`,
          ticketNumber: 66,
        }),
        {
          tag: [
            DataManager.Consts.Tags.QA_TAG,
            DataManager.Consts.Tags.DEV_TAG,
            DataManager.Consts.Tags.SANITY_TAG,
            DataManager.Consts.Tags.CONSUMER_APP_TAG,
            DataManager.Consts.Tags.BUSINESS_APP_TAG,
            DataManager.Consts.Tags.CONSUMER_DROP_TAG,
          ],
        },
        async ({ smallPcPage, mobilePage }) => {
          const site = DataManager.Consts.Sites.LocationOperatorUserSite;
          const admin = new LocationOperatorUser(smallPcPage);
          const consumer = new ConsumerUser(mobilePage, 'Sadfa Asdfa', DataManager.Consts.PHONE_NUMBER3, site, {
            dropType: DropTypesEnum.Delivery,
            address: '123 Main St',
          });

          await consumer.perform.existingConsumerDroppingAPackage({
            packages: DataManager.DataGenerator.generatePackages(site, numOfPackagesToGenerate),
          });

          await admin.perform.loginAndReceivePackages({
            site: consumer.site,
            packages: consumer.droppedPackages,
            shouldSearchPackages: shouldSearchPackage,
          });

          await admin.perform.sealPackage({
            site: consumer.site,
            packagee: consumer.droppedPackages[0],
            shouldGoToOperationHubAfterAction: true,
          });

          await admin.perform.prepareAndDispatchDelivery({
            site: consumer.site,
            packages: consumer.droppedPackages,
            shouldSearchPackages: shouldSearchPackage,
          });

          await admin.perform.deliverPackages({
            site: consumer.site,
            packagee: consumer.droppedPackages[0],
            shouldGoToOperationHubAfterAction: false,
          });

          await admin.assert.deliveryStatus({
            packagee: consumer.droppedPackages[0],
            site: consumer.site,
            shouldNavigateToPage: false,
            assertion: (status) =>
              expect(status, { message: 'Asserting drop status' }).toBe(DeliveryStatusEnum.Delivered),
          });
        },
      );
    }
  }
});

import { expect } from '@playwright/test';
import LocationOperatorUser from '../../testsInfra/actors/locationOperatorUser';
import DataManager from '../../testsInfra/dataManager';
import { test } from '../baseTest';

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: 'Location Operator Admin Will be able to use filters relevant to their site in stores page',
    ticketNumber: 66,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
    ],
  },
  async ({ smallPcPage }) => {
    const admin = new LocationOperatorUser(smallPcPage);

    await admin.perform.login();
    await admin.goTo.operationsHubPage({ site: DataManager.Consts.Sites.LocationOperatorUserSite, afterLogin: true });
    await admin.perform.filterStoresByName({ shouldNavigateToPage: true, text: 'CA' });
    await admin.assert.storeCardNames({
      shouldNavigateToPage: false,
      expectedMinimumResultCount: 1,
      assertion: (text) => expect(text, { message: 'Asserting store name' }).toContain('CA'),
    });
  },
);

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: 'Global Admin Will will be able to use filters relevant to all sites in stores page',
    ticketNumber: 66,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
    ],
  },
  async ({ smallPcPage }) => {
    const admin = new LocationOperatorUser(smallPcPage);

    await admin.perform.login();
    await admin.goTo.operationsHubPage({ site: DataManager.Consts.Sites.LocationOperatorUserSite, afterLogin: true });
    await admin.goTo.storesViewPage();
    // TODO: Add assertions
  },
);

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: 'Location Operator Admin Will be able to use filters relevant to their deliveries in deliveries page',
    ticketNumber: 66,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
    ],
  },
  async ({ smallPcPage }) => {
    const admin = new LocationOperatorUser(smallPcPage);

    await admin.perform.login();
    await admin.goTo.operationsHubPage({ site: DataManager.Consts.Sites.LocationOperatorUserSite, afterLogin: true });
    await admin.goTo.storesViewPage();
    // TODO: Add assertions
  },
);

test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: "Global Admin Will will be able to use filters relevant to a site's deliveries in deliveries page",
    ticketNumber: 66,
  }),
  {
    tag: [
      DataManager.Consts.Tags.QA_TAG,
      DataManager.Consts.Tags.DEV_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.BUSINESS_APP_TAG,
    ],
  },
  async ({ smallPcPage }) => {
    const admin = new LocationOperatorUser(smallPcPage);

    await admin.perform.login();
    await admin.goTo.operationsHubPage({ site: DataManager.Consts.Sites.LocationOperatorUserSite, afterLogin: true });
    await admin.goTo.deliveriesPage();
    // TODO: Add assertions
  },
);

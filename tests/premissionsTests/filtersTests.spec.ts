import { expect } from '@playwright/test';
import GlobalAdmin from '../../testsInfra/actors/globalAdmin';
import LocationOperatorUser from '../../testsInfra/actors/locationOperatorUser';
import DataManager from '../../testsInfra/dataManager';
import LocationTypesEnum from '../../testsInfra/enums/locationTypesEnum';
import { test } from '../baseTest';

test.describe('Filters in Stores View', () => {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Location Operator Admin will be able to filter by name',
      ticketNumber: 66,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SMOKE_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
      ],
    },
    async ({ smallPcPage }) => {
      const storeName = 'CA';
      const admin = new LocationOperatorUser(smallPcPage);

      await admin.perform.login();
      await admin.perform.filterStoresByName({ shouldNavigateToPage: true, text: storeName });
      await admin.assert.storeCardNames({
        assertion: (text) => expect(text, { message: 'Asserting store name' }).toContain(storeName),
      });

      await admin.assert.storeCardLocations({
        assertion: (location) => {
          expect
            .soft(location, `Card name should contain "${LocationTypesEnum.Store}"`)
            .toContain(LocationTypesEnum.Store);
        },
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Global Admin Will will be able to use all filters',
      ticketNumber: 66,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SMOKE_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
      ],
    },
    async ({ smallPcPage }) => {
      const expectedKeyWordsRegexForStoreFilter = /CA|Vancouver/i;
      const expectedVendorName = 'CA Bags N All (Lightspeed)';
      const expectedGroupName = 'Kappa Toys';
      const admin = new GlobalAdmin(smallPcPage);

      await admin.perform.login();
      await admin.perform.filterStores({ shouldNavigateToPage: true, siteName: 'CA Smart Vancouver' });
      await admin.assert.storeCardNames({
        assertion: (text) => {
          expect
            .soft(text, 'Store name should contain "CA" or "Vancouver"')
            .toMatch(expectedKeyWordsRegexForStoreFilter);
        },
      });

      await admin.perform.filterStores({ VendorName: expectedVendorName });
      await admin.assert.storeCardNames({
        assertion: (text) => {
          expect.soft(text, `Store name should contain "${expectedVendorName}"`).toContain(expectedVendorName);
        },
      });

      await admin.perform.filterStores({ GroupName: expectedGroupName });
      await admin.assert.storeCardNames({
        assertion: (text) => {
          expect.soft(text, `Store name should contain "${expectedGroupName}"`).toContain(expectedGroupName);
        },
      });

      await admin.perform.filterStores({ LocationType: LocationTypesEnum.Store });
      await admin.assert.storeCardLocations({
        assertion: (location) => {
          expect
            .soft(location, `Card name should contain "${LocationTypesEnum.Store}"`)
            .toContain(LocationTypesEnum.Store);
        },
      });

      await admin.perform.filterStores({ LocationType: LocationTypesEnum.Warehouse });
      await admin.assert.storeCardLocations({
        assertion: (location) => {
          expect
            .soft(location, `Card name should contain "${LocationTypesEnum.Warehouse}"`)
            .toContain(LocationTypesEnum.Warehouse);
        },
      });
    },
  );
});

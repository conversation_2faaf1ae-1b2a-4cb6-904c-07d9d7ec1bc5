import { expect } from '@playwright/test';
import { ConsumerUser } from '../../testsInfra/actors/consumer';
import GlobalAdmin from '../../testsInfra/actors/globalAdmin';
import DataManager from '../../testsInfra/dataManager';
import DeliveryStatusEnum from '../../testsInfra/enums/deliveryStatusEnum';
import DropTypesEnum from '../../testsInfra/enums/dropTypesEnum';
import { test } from '../baseTest';
import { ConsumerConsts } from './sharedConsts';

const howToEnterAppVariations = [
  {
    name: 'Scanning through app',
    shouldScanQRCodeThroughAppFirstStore: true,
    shouldScanQRCodeThroughAppSecondStore: true,
  },
  {
    name: 'Scanning outside of app',
    shouldScanQRCodeThroughAppFirstStore: false,
    shouldScanQRCodeThroughAppSecondStore: false,
  },
  {
    name: 'Scanning through app first store, outside of app second store',
    shouldScanQRCodeThroughAppFirstStore: true,
    shouldScanQRCodeThroughAppSecondStore: false,
  },
  {
    name: 'Scanning outside of app first store, through app second store',
    shouldScanQRCodeThroughAppFirstStore: false,
    shouldScanQRCodeThroughAppSecondStore: true,
  },
];

for (const howToEnterAppVariation of howToEnterAppVariations) {
  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: `Two drops in single delivery Collection only site, ${howToEnterAppVariation.name}`,
      ticketNumber: 64,
      additionalTickets: [104, 105],
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.DEV_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.CONSUMER_APP_TAG,
        DataManager.Consts.Tags.BUSINESS_APP_TAG,
        DataManager.Consts.Tags.CONSUMER_DROP_TAG,
      ],
    },
    async ({ smallPcPage, mobilePage }) => {
      const site = DataManager.Consts.Sites.CollectionOnlySite;
      const admin = new GlobalAdmin(smallPcPage);
      const consumer = new ConsumerUser(mobilePage, 'Automation User', DataManager.Consts.PHONE_NUMBER1, site, {
        dropType: DropTypesEnum.Collection,
      });

      await consumer.perform.existingConsumerDroppingAPackage({
        shouldFinishDropping: false,
        store: consumer.site.stores[0],
        shouldScanQRCodeThroughApp: howToEnterAppVariation.shouldScanQRCodeThroughAppFirstStore,
      });

      await consumer.perform.existingConsumerDroppingAPackage({
        shouldLogin: false,
        store: consumer.site.stores[1],
        shouldScanQRCodeThroughApp: howToEnterAppVariation.shouldScanQRCodeThroughAppSecondStore,
      });

      await admin.perform.loginAndReceivePackages({ site: consumer.site, packages: consumer.droppedPackages });
      await admin.perform.handoverPackages({ site: consumer.site, packages: consumer.droppedPackages });

      await admin.assert.deliveryStatus({
        packagee: consumer.droppedPackages[0],
        site: consumer.site,
        assertion: (status) =>
          expect(status, { message: ConsumerConsts.assertTitlePackageStatus }).toBe(DeliveryStatusEnum.Delivered),
      });
    },
  );
}

import { test as baseTest, BrowserContext, chromium, devices, Page } from '@playwright/test';
import DataManager from '../testsInfra/dataManager';
import HealthCheckApi from '../testsInfra/innerInfra/api/healthCheckApi';
import { Logger } from '../testsInfra/innerInfra/logger';

declare global {
  interface Window {
    verificationCallback: (response: string) => void;
    errorCallback: () => void;
    isPlaywrightTest: boolean;
  }
}

type Fixtures = {
  smallPcPage: Page;
  midPcPage: Page;
  mobilePage: Page;
  mobilePageWithCameraPermissions: Page;
};

const healthCheckApi = new HealthCheckApi();
const getTestResultMessage = (testInfo) => {
  switch (testInfo.status) {
    case 'passed':
      return '✅ Test Passed';

    case 'failed':
      return '❌ Test Failed';

    case 'skipped':
      return '⏭️ Skipped Test';

    case 'timedOut':
      return '⌛ Test Timed Out';

    case 'interrupted':
      return '⛔ Test Interrupted';

    default:
      return `❔ Unknown Test Result, ${testInfo.status}`;
  }
};

const test = baseTest.extend<Fixtures>({
  smallPcPage: async ({ browser }, use) => {
    const context: BrowserContext = await browser.newContext({
      viewport: { width: 1280, height: 720 },
    });

    const page: Page = await context.newPage();
    await use(page);
    await context.close();
  },

  midPcPage: async ({ browser }, use) => {
    const context: BrowserContext = await browser.newContext({
      viewport: { width: 1920, height: 1080 },
    });

    const page: Page = await context.newPage();
    await use(page);
    await context.close();
  },

  mobilePageWithCameraPermissions: async ({}, use) => {
    const width = 430;
    const height = 932;

    // Commands to create a well scaled video using ffmpeg:
    /* ffmpeg \
        -loop 1 -t 3 -i media/qrCode.png \
        -loop 1 -t 3 -i media/packageBarcode.png \
        -filter_complex "\
        [0:v]scale=w=393:h=659:force_original_aspect_ratio=decrease,\
        pad=w=393:h=659:x=(ow-iw)/2:y=(oh-ih)/2:color=black[qr]; \
        [1:v]scale=w=393:h=659:force_original_aspect_ratio=decrease,\
        pad=w=393:h=659:x=(ow-iw)/2:y=(oh-ih)/2:color=black[bar]; \
        [qr][bar]concat=n=2:v=1[out]" \
        -map "[out]" -pix_fmt yuv420p output.mp4

        // Scaling to proper dimensions for the phone:
        ffmpeg -i output.mp4 -vf "scale='if(gt(a,393/670),393,-2)':'if(gt(a,393/670),-2,670)'" -c:a copy output_scaled.mp4

        // Converting to y4m format:
        ffmpeg -i output_scaled.mp4 -pix_fmt yuv420p videoStream.y4m
        
        // Make sure to delete the mp4 files after finishing using them
    */
    // We are using a 6 seconds video that cycled between the barcode and qrcode every 3 seconds
    // to not be dependent on load times
    // The Automation's waits on default 5 seconds for things to appear, so we can be sure that the needed image will
    // be scanned in the right time

    const browser = await chromium.launch({
      headless: process.env.CI ? true : false,
      args: [
        '--disable-web-security',
        '--use-fake-ui-for-media-stream',
        '--use-fake-device-for-media-stream',
        `--use-file-for-fake-video-capture=videoStream.y4m`, // This is the video that will be used for the test, works only on qa
      ],
    });

    const context = await browser.newContext({
      viewport: { width, height },
      permissions: ['camera'],
    });

    const page = await context.newPage();

    await use(page);

    await context.close();
    await browser.close();
  },

  mobilePage: async ({ browser }, use) => {
    const context: BrowserContext = await browser.newContext(devices['iPhone 15 Pro']);

    const page: Page = await context.newPage();

    await use(page);
    await context.close();
  },
});

const getEnvStatus = async () => {
  try {
    await healthCheckApi.verifyAllServicesAreUp();

    return { isEnvUp: true, message: 'Environment is up' };
  } catch (error) {
    return { isEnvUp: false, message: error.message };
  }
};
const getMessageIfWrongEnv = (canTestRunInEnv: boolean, expectedEnv: string) => {
  if (!canTestRunInEnv && expectedEnv === DataManager.Consts.TESTING_ENV) {
    const wrongTagErrorMessage = wrongTagErrorMessageFormat(expectedEnv);
    Logger.warning(wrongTagErrorMessage);

    return wrongTagErrorMessage;
  }

  return null;
};

const wrongTagErrorMessageFormat = (expectedTag) =>
  `Test not tagged with ${expectedTag} but running in ${DataManager.Consts.TESTING_ENV} environment, test will be skipped`;

test.beforeEach(async ({ context }, testInfo) => {
  const tags = testInfo.tags;
  const canTestRunInQA = tags?.includes(DataManager.Consts.Tags.QA_TAG);
  const canTestRunInDev = tags?.includes(DataManager.Consts.Tags.DEV_TAG);
  const canTestRunInStaging = tags?.includes(DataManager.Consts.Tags.STAGING_TAG);
  const envStatus = await getEnvStatus();

  Logger.step(`____________________________________________________________________________________________________\n`);
  Logger.step(`🧪 Starting test: ${testInfo.title}`);
  Logger.step(`____________________________________________________________________________________________________\n`);

  Logger.info(envStatus.message);
  test.skip(!envStatus.isEnvUp, envStatus.message);

  const qaMessage = getMessageIfWrongEnv(canTestRunInQA, DataManager.Consts.QA_ENV);
  const devMessage = getMessageIfWrongEnv(canTestRunInDev, DataManager.Consts.DEV_ENV);
  const stagingMessage = getMessageIfWrongEnv(canTestRunInStaging, DataManager.Consts.STAGING_ENV);

  // Tests will be skipped if the set environment is not the one the test is supposed to run in
  test.skip(!!qaMessage, qaMessage!);
  test.skip(!!devMessage, devMessage!);
  test.skip(!!stagingMessage, stagingMessage!);

  // We are adding this script to the context so that our app recognizes we are in a playwright test
  // And not take the automation into anything it does not need to
  await context.addInitScript(() => (window.isPlaywrightTest = true));
});

test.afterEach(async ({}, testInfo) => {
  Logger.step(`____________________________________________________________________________________________________\n`);
  Logger.step(`${getTestResultMessage(testInfo)}: ${testInfo.title}, Duration: ${testInfo.duration}ms`);
  Logger.step('____________________________________________________________________________________________________\n');
});

export { test };

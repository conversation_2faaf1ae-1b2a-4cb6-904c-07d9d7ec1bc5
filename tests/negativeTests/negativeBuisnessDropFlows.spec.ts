import { expect } from '@playwright/test';
import { BusinessOwnerUser } from '../../testsInfra/actors/businessOwner';
import GlobalAdminUser from '../../testsInfra/actors/globalAdmin';
import DataManager from '../../testsInfra/dataManager';
import Package from '../../testsInfra/entities/package';
import DeliveryStatusEnum from '../../testsInfra/enums/deliveryStatusEnum';
import { test } from '../baseTest';

const testsData = [
  {
    country: 'Canada',
    address: 'Osama <PERSON> 22', // The Canadians consider <PERSON><PERSON><PERSON> as a terrorist, they would never has an address with his name
  },
];

test.describe('Negative Business Drop Flows', () => {
  for (const currTestData of testsData) {
    test(
      DataManager.DataConverter.convertToTestTitleName({
        titleText: `Drop with an invalid Address in ${currTestData.country}`,
        ticketNumber: 46,
      }),
      {
        tag: [
          DataManager.Consts.Tags.QA_TAG,
          DataManager.Consts.Tags.NEGATIVE_TAG,
          DataManager.Consts.Tags.SANITY_TAG,
          DataManager.Consts.Tags.FULFILLMENT_APP_TAG,
        ],
      },
      async ({ page }) => {
        const site = DataManager.Consts.Sites.BusinessDropSite;
        const bizUser = new BusinessOwnerUser(page, site, {
          address: currTestData.address,
        });

        await bizUser.perform.login();
        await bizUser.perform.showFulfillmentUI();
        await bizUser.goTo.deliveryPage();
        expect(
          await bizUser.perform.addAddress({
            address: currTestData.address,
            additionalContactInfo: 'Additional Contact Info',
            shouldThrowError: false,
            shouldClickOnSave: false,
          }),
          'Asserting if invalid address was found',
        ).toBeFalsy();
      },
    );
  }

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Drop with an invalid Package code',
      ticketNumber: 92,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.FULFILLMENT_APP_TAG,
      ],
    },
    async ({ page }) => {
      const site = DataManager.Consts.Sites.BusinessDropSite;
      const bizUser = new BusinessOwnerUser(page, site);
      const InvalidPackages = DataManager.DataGenerator.generateInvalidPackageCodes(site);

      await bizUser.perform.login();
      await bizUser.perform.showFulfillmentUI();
      await bizUser.perform.dropPackages({
        firstName: 'Arya',
        lastName: 'Stark',
        phoneNumber: DataManager.Consts.PHONE_NUMBER2,
        country: 'Israel',
        packages: [InvalidPackages[0]],
        shouldAllPackageAdditionSucceed: false,
      });

      await bizUser.assert.invalidPackageCodes(InvalidPackages, (errorMessage, code) =>
        expect
          .soft(errorMessage, {
            message: `Asserting the invalid package code: ${code}\nif the received string is empty no error message appeared`,
          })
          .toContain('Bag ID is not valid'),
      );
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Dropping with an invalid and valid package code will successfully drop the valid one',
      ticketNumber: 101,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.FULFILLMENT_APP_TAG,
      ],
    },
    async ({ page, smallPcPage }) => {
      const site = DataManager.Consts.Sites.BusinessDropSite;
      const bizUser = new BusinessOwnerUser(page, site);
      const adminUser = new GlobalAdminUser(smallPcPage);
      const inValidPackage = [new Package('123456789')];
      const validPackage = DataManager.DataGenerator.generatePackages(site, 1);

      await bizUser.perform.login();
      await bizUser.perform.showFulfillmentUI();
      await bizUser.perform.dropPackages({
        firstName: 'Arya',
        lastName: 'Stark',
        phoneNumber: DataManager.Consts.PHONE_NUMBER2,
        country: 'Israel',
        packages: inValidPackage,
        shouldAllPackageAdditionSucceed: false,
      });

      await bizUser.perform.enterPackageCode(validPackage[0], {
        shouldPackageAdditionFail: false,
      });

      await bizUser.perform.checkout();

      await bizUser.perform.dispatchPackages({ packages: validPackage });
      await adminUser.perform.deliverPackages({
        isLoggedIn: false,
        site: bizUser.site,
        packagee: validPackage[0],
        shouldGoToOperationHub: true,
        shouldGoToOperationHubAfterAction: false,
      });

      await bizUser.assert.deliveryStatus({
        packagee: validPackage[0],
        assertion: {
          expectFunc: (status, expectedStatus) =>
            expect(status, { message: 'Asserting drop status' }).toBe(expectedStatus),
          expectedStatus: DeliveryStatusEnum.Delivered,
        },
      });
    },
  );

  test(
    DataManager.DataConverter.convertToTestTitleName({
      titleText: 'Drop with an empty Package code',
      ticketNumber: 92,
    }),
    {
      tag: [
        DataManager.Consts.Tags.QA_TAG,
        DataManager.Consts.Tags.NEGATIVE_TAG,
        DataManager.Consts.Tags.SANITY_TAG,
        DataManager.Consts.Tags.FULFILLMENT_APP_TAG,
      ],
    },
    async ({ page }) => {
      const site = DataManager.Consts.Sites.BusinessDropSite;
      const bizUser = new BusinessOwnerUser(page, site);
      const InvalidPackage = new Package('');

      await bizUser.perform.login();
      await bizUser.perform.showFulfillmentUI();
      const addedPackages = await bizUser.perform.dropPackages({
        firstName: 'Arya',
        lastName: 'Stark',
        phoneNumber: DataManager.Consts.PHONE_NUMBER2,
        country: 'Israel',
        packages: [InvalidPackage],
        shouldAllPackageAdditionSucceed: false,
      });

      expect(addedPackages).toHaveLength(0);
    },
  );
});

import { expect } from '@playwright/test';
import { BusinessOwnerUser } from '../../testsInfra/actors/businessOwner';
import GlobalAdmin from '../../testsInfra/actors/globalAdmin';
import DataManager from '../../testsInfra/dataManager';
import DeliveryStatusEnum from '../../testsInfra/enums/deliveryStatusEnum';
import { test } from '../baseTest';

// Notes:
// Need to Add Dropit courier with same day delivery in staging for the test to work
// Need to also add the BVA barcode
test(
  DataManager.DataConverter.convertToTestTitleName({
    titleText: 'Bicester Village Happy flow in fulfillment',
    ticketNumber: 107,
  }),
  {
    tag: [
      DataManager.Consts.Tags.STAGING_TAG,
      DataManager.Consts.Tags.SANITY_TAG,
      DataManager.Consts.Tags.FULFILLMENT_APP_TAG,
    ],
  },
  async ({ page, smallPcPage }) => {
    const businessOwner = new BusinessOwnerUser(page, DataManager.Consts.Sites.BusinessDropSite);
    const admin = new GlobalAdmin(smallPcPage);

    const droppedPackages = await businessOwner.perform.droppingAPackageForConsumer();
    await businessOwner.perform.dispatchPackages({ packages: droppedPackages });
    await businessOwner.assert.deliveryStatus({
      packagee: droppedPackages[0],
      assertion: {
        expectFunc: (status, expectedStatus) =>
          expect(status, { message: 'Asserting drop status to be on the way' }).toBe(expectedStatus),
        expectedStatus: DeliveryStatusEnum.OnTheWay,
      },
      shouldUseClickForNavigation: true,
    });

    await admin.perform.deliverPackages({
      isLoggedIn: false,
      site: businessOwner.site,
      packagee: droppedPackages[0],
      shouldGoToOperationHub: true,
      shouldGoToOperationHubAfterAction: false,
    });

    await businessOwner.assert.deliveryStatus({
      packagee: droppedPackages[0],
      assertion: {
        expectFunc: (status, expectedStatus) =>
          expect(status, { message: 'Asserting drop status to be delivered' }).toBe(expectedStatus),
        expectedStatus: DeliveryStatusEnum.Delivered,
      },
    });
  },
);

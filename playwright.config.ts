import { defineConfig, devices } from '@playwright/test';
import dotenv from 'dotenv';
import DataManager from './testsInfra/dataManager';

dotenv.config({ path: 'envFiles/common.env' });

// process.env.ENV is a pipeline only variable
const envName = process.env.ENV ?? process.env.TESTING_ENV;
if (!envName) {
  throw new Error('❌ TESTING_ENV is not set');
}

dotenv.config({ path: `envFiles/${envName}.env`, override: true });

DataManager.Consts.throwErrorIfAnyRequiredEnvVarIsNotSet();

export default defineConfig({
  globalSetup: './healthCheck.ts',
  testDir: './tests',
  timeout: 180000,
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 1 : 0,
  /* Opt out of parallel tests, to restore replace 1 with undefined */
  workers: 1,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: process.env.CI
    ? [
        ['junit', { outputFile: 'results.xml' }] /* XML file is necessary for Semaphore Tests UI  */,
        ['dot'], // This is to output the tests to the pipeline logs
        ['html', { open: 'never' }],
        [
          'playwright-zephyr/lib/src/cloud',
          {
            projectKey: 'TMTN',
            authorizationToken: DataManager.Consts.ZEPHYR_AUTH_TOKEN,
            testCycle: {
              name: `${DataManager.DataGenerator.todayDate()}, ${DataManager.Consts.TESTING_ENV}: ${process.env.SEMAPHORE_GIT_BRANCH}}`,
              customFields: {
                Automated: ['true'],
              },
            },
          },
        ],
      ]
    : 'html',
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    trace: process.env.CI ? 'retain-on-failure' : 'on',
    headless: process.env.CI ? true : false,
    actionTimeout: 45000,
  },

  projects: [
    {
      name: 'default',
      use: {
        ...devices['Desktop Chrome'],
        permissions: ['camera'],
      },

      grepInvert: new RegExp(DataManager.Consts.Tags.FULFILLMENT_APP_TAG),
    },

    {
      name: 'desktop',
      use: {
        ...devices['Desktop Chrome'],
        permissions: ['camera'],
      },

      grep: new RegExp(DataManager.Consts.Tags.FULFILLMENT_APP_TAG),
    },

    {
      name: 'mobile',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 375, height: 812 },
        permissions: ['camera'],
      },

      grep: new RegExp(DataManager.Consts.Tags.FULFILLMENT_APP_TAG),
    },
  ],
});

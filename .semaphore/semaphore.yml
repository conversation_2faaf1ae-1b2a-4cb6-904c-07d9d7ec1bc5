version: v1.0
name: <PERSON><PERSON> Lint and Test
agent:
  machine:
    type: e1-standard-2
    os_image: ubuntu2004
global_job_config:
  prologue:
    commands:
      - sem-version node 22
blocks:
  - name: Install Sops and Reveal
    task:
      jobs:
        - name: Install Sops and reveal .env file
          commands:
            - checkout
            - chmod +x ../installSecretV3.sh
            - ../installSecretV3.sh
      secrets:
        - name: SOPS
        - name: GCP
      prologue:
        commands:
          - gcloud auth activate-service-account --key-file=.secrets/semaphore2.json
          - gcloud auth configure-docker -q
          - echo "Download scripts from $SCRIPTS_BUCKET"
          - 'gsutil cp -r gs://$SCRIPTS_BUCKET/v2/installSecretV3.sh /home/<USER>/'
      env_vars:
        - name: ENV
          value: qa
    dependencies: []
  - name: Playwright Lint
    dependencies:
      - Install Sops and Reveal
    task:
      jobs:
        - name: Play<PERSON> Lint
          commands:
            - checkout
            - npm ci
            - echo "Running Lint for $SEMAPHORE_GIT_BRANCH"
            - 'npx eslint . --ext .js,.ts -f junit -o report-lint.xml'
      epilogue:
        always:
          commands:
            - '[[ -f report-lint.xml ]] && test-results publish report-lint.xml'
    run:
      when: branch =~ '^feature/' OR branch =~ '^bugfix/' OR branch =~ '^hotfix/' OR pull_request =~ '.*'
after_pipeline:
  task:
    jobs:
      - name: Publish Results
        commands:
          - test-results gen-pipeline-report
promotions:
  - name: Playwright Test
    pipeline_file: playwright-tests-task.yml

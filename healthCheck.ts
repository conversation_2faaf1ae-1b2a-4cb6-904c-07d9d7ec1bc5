import DataManager from './testsInfra/dataManager';
import HealthCheckApi from './testsInfra/innerInfra/api/healthCheckApi';
import { Logger } from './testsInfra/innerInfra/logger';

async function globalSetup() {
  const healthCheckApi = new HealthCheckApi();

  try {
    await healthCheckApi.verifyAllServicesAreUp();
    Logger.info(`✅ All services in ${DataManager.Consts.TESTING_ENV} are up. Proceeding with tests.`);
  } catch (error) {
    Logger.error(`❌ Health check failed. Tests will NOT run.\n${error.message}`);
    throw new Error(`❌ Health check failed. Tests will NOT run.\n${error.message}`);
  }
}

export default globalSetup;
